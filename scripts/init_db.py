#!/usr/bin/env python3
"""
Database initialization script.
Creates the database tables and runs initial migrations.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from app.core.database import Base
from app.core.config import settings

# Import all models to ensure they are registered
from app.models.vendor import Vendor
from app.models.category import Category
from app.models.product import Product, ProductImage, ProductVariant


def create_tables():
    """Create all database tables."""
    print("Creating database tables...")
    
    engine = create_engine(settings.database_url)
    Base.metadata.create_all(bind=engine)
    
    print("Database tables created successfully!")


if __name__ == "__main__":
    create_tables()
