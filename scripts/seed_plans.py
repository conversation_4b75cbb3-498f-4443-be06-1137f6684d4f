#!/usr/bin/env python3
"""
Script to seed default subscription plans.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import <PERSON>Local
from app.services.monetization import plan_service
from app.schemas.monetization import PlanCreate
from app.models.monetization import PlanType, PlanDuration


def seed_plans():
    """Create default subscription plans."""
    db: Session = SessionLocal()
    
    try:
        # Default plans configuration
        plans_config = [
            {
                "name": "Free Plan",
                "description": "Basic features for getting started",
                "plan_type": PlanType.FREE,
                "duration": PlanDuration.MONTHLY,
                "price": 0.00,
                "currency": "TZS",
                "max_products": 5,
                "max_images_per_product": 3,
                "max_file_uploads_mb": 50,
                "chat_support": False,
                "analytics_access": False,
                "priority_listing": False,
                "features": {
                    "basic_listing": True,
                    "customer_messaging": True,
                    "basic_analytics": False,
                    "priority_support": False
                }
            },
            {
                "name": "Basic Plan",
                "description": "Perfect for small businesses",
                "plan_type": PlanType.BASIC,
                "duration": PlanDuration.MONTHLY,
                "price": 15000.00,  # 15,000 TZS per month
                "currency": "TZS",
                "max_products": 25,
                "max_images_per_product": 5,
                "max_file_uploads_mb": 200,
                "chat_support": True,
                "analytics_access": True,
                "priority_listing": False,
                "features": {
                    "basic_listing": True,
                    "customer_messaging": True,
                    "basic_analytics": True,
                    "priority_support": True,
                    "bulk_upload": True
                }
            },
            {
                "name": "Premium Plan",
                "description": "Advanced features for growing businesses",
                "plan_type": PlanType.PREMIUM,
                "duration": PlanDuration.MONTHLY,
                "price": 35000.00,  # 35,000 TZS per month
                "currency": "TZS",
                "max_products": 100,
                "max_images_per_product": 10,
                "max_file_uploads_mb": 1000,
                "chat_support": True,
                "analytics_access": True,
                "priority_listing": True,
                "features": {
                    "basic_listing": True,
                    "customer_messaging": True,
                    "advanced_analytics": True,
                    "priority_support": True,
                    "bulk_upload": True,
                    "priority_listing": True,
                    "custom_branding": True,
                    "api_access": True
                }
            },
            {
                "name": "Enterprise Plan",
                "description": "Full-featured solution for large businesses",
                "plan_type": PlanType.ENTERPRISE,
                "duration": PlanDuration.MONTHLY,
                "price": 75000.00,  # 75,000 TZS per month
                "currency": "TZS",
                "max_products": 500,
                "max_images_per_product": 20,
                "max_file_uploads_mb": 5000,
                "chat_support": True,
                "analytics_access": True,
                "priority_listing": True,
                "features": {
                    "basic_listing": True,
                    "customer_messaging": True,
                    "advanced_analytics": True,
                    "priority_support": True,
                    "bulk_upload": True,
                    "priority_listing": True,
                    "custom_branding": True,
                    "api_access": True,
                    "white_label": True,
                    "dedicated_support": True,
                    "custom_integrations": True
                }
            }
        ]
        
        print("Creating default subscription plans...")
        
        created_count = 0
        for plan_config in plans_config:
            try:
                # Check if plan already exists
                existing_plans = plan_service.get_plans(db, active_only=False)
                plan_exists = any(
                    plan.name == plan_config["name"] 
                    for plan in existing_plans.plans
                )
                
                if plan_exists:
                    print(f"⚠️  Plan '{plan_config['name']}' already exists, skipping...")
                    continue
                
                # Create plan
                plan_create = PlanCreate(**plan_config)
                created_plan = plan_service.create_plan(db, plan_create)
                
                print(f"✅ Created plan: {created_plan.name}")
                print(f"   Type: {created_plan.plan_type}")
                print(f"   Price: {created_plan.price} {created_plan.currency}")
                print(f"   Max Products: {created_plan.max_products}")
                print(f"   Features: {list(created_plan.features.keys()) if created_plan.features else 'None'}")
                print()
                
                created_count += 1
                
            except Exception as e:
                print(f"❌ Error creating plan '{plan_config['name']}': {str(e)}")
        
        print(f"✅ Successfully created {created_count} subscription plans!")
        
        if created_count > 0:
            print("\n📋 Plan Summary:")
            all_plans = plan_service.get_plans(db, active_only=True)
            for plan in all_plans.plans:
                print(f"- {plan.name}: {plan.price} {plan.currency}/month ({plan.max_products} products)")
        
    except Exception as e:
        print(f"❌ Error seeding plans: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    seed_plans()
