#!/usr/bin/env python3
"""
Script to seed the initial admin user.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.vendor import vendor_service
from app.schemas.vendor import VendorCreate
from app.models.vendor import Vendor<PERSON>ole


def seed_admin():
    """Create the initial admin user."""
    db: Session = SessionLocal()
    
    try:
        # Admin user details
        admin_data = {
            "business_name": "System",
            "owner_name": "Admin",
            "email": "<EMAIL>",
            "phone": "0746482609",
            "description": "The System Owner",
            "location": "Global",
            "password": "1234567890"
        }
        
        print("Creating initial admin user...")
        print(f"Phone: {admin_data['phone']}")
        print(f"Email: {admin_data['email']}")
        
        # Check if admin already exists
        existing_admin = vendor_service.get_vendor_by_phone(db, admin_data["phone"])
        if existing_admin:
            print("❌ Admin user already exists!")
            print(f"Existing admin: {existing_admin.business_name} ({existing_admin.phone})")
            return
        
        # Create admin user
        vendor_create = VendorCreate(**admin_data)
        admin_user = vendor_service.create_admin_vendor(db, vendor_create)
        
        print("✅ Admin user created successfully!")
        print(f"ID: {admin_user.id}")
        print(f"Business Name: {admin_user.business_name}")
        print(f"Owner Name: {admin_user.owner_name}")
        print(f"Phone: {admin_user.phone}")
        print(f"Email: {admin_user.email}")
        print(f"Role: {admin_user.role}")
        print(f"Status: {admin_user.status}")
        print("\n🔑 Login credentials:")
        print(f"Phone: {admin_data['phone']}")
        print(f"Password: {admin_data['password']}")
        
    except Exception as e:
        print(f"❌ Error creating admin user: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    seed_admin()
