# Dealer AI Backend

A vendor-customer marketplace platform integrated with WhatsApp Business API, AI-powered product recommendations, and Supabase for file storage.

## Features

### Milestone 1 - Core Platform
- **Vendor Management**: Registration, phone-based authentication, profile management
- **Product Management**: CRUD operations with images and variants
- **Category Management**: Product categorization system (admin-only)
- **JWT Authentication**: Secure vendor authentication with phone numbers
- **Role-Based Access Control**: Admin and vendor roles with proper permissions
- **Clean Architecture**: Separation of concerns for scalability
- **Database**: PostgreSQL with SQLAlchemy ORM and Alembic migrations
- **API Documentation**: Automatic Swagger UI documentation

### Milestone 2 - Messaging & Customer Management
- **Customer Management**: Customer profiles with phone-based identification
- **Chat System**: Vendor-customer messaging with product context
- **Message Management**: Send, receive, update, and delete messages
- **File Uploads**: Local file storage for attachments (images, documents, audio, video)
- **Message Status Tracking**: Sent, delivered, read status management
- **Soft Deletes**: Data integrity with soft delete functionality

### Milestone 3 - Monetization Core
- **Subscription Plans**: Flexible plan system with features and limits
- **Subscription Management**: Vendor subscriptions with auto-renewal
- **In-App Purchases**: One-time purchases for premium features
- **Payment Processing**: Manual payment status management (ready for gateway integration)
- **Usage Tracking**: Monitor plan limits and usage statistics
- **Premium Feature Protection**: Subscription-based access control
- **Automated Plan Expiry**: Scheduled jobs for subscription management
- **Scalable Architecture**: Ready for external payment gateway integration

## Project Structure

```
dealer_backend/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/
│   │       │   ├── auth.py
│   │       │   ├── vendors.py
│   │       │   ├── categories.py
│   │       │   ├── products.py
│   │       │   ├── customers.py
│   │       │   ├── chats.py
│   │       │   ├── messages.py
│   │       │   └── uploads.py
│   │       └── api.py
│   ├── core/
│   │   ├── auth.py
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── security.py
│   │   ├── permissions.py
│   │   ├── phone_validation.py
│   │   └── file_storage.py
│   ├── models/
│   │   ├── vendor.py
│   │   ├── category.py
│   │   ├── product.py
│   │   ├── customer.py
│   │   └── chat.py
│   ├── repositories/
│   │   ├── base.py
│   │   ├── vendor.py
│   │   ├── category.py
│   │   ├── product.py
│   │   ├── customer.py
│   │   └── chat.py
│   ├── schemas/
│   │   ├── vendor.py
│   │   ├── category.py
│   │   ├── product.py
│   │   ├── customer.py
│   │   └── chat.py
│   └── services/
│       ├── vendor.py
│       ├── category.py
│       ├── product.py
│       ├── customer.py
│       ├── chat.py
│       └── message.py
├── scripts/
│   ├── init_db.py
│   └── seed_admin.py
├── uploads/
│   └── .gitkeep
├── alembic/
│   ├── versions/
│   └── env.py
├── tests/
│   ├── conftest.py
│   └── test_main.py
├── main.py
├── requirements.txt
└── .env
```

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Database Setup

1. Create a PostgreSQL database:
```sql
CREATE DATABASE dealer_db;
```

2. Update the `.env` file with your database credentials:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/dealer_db
SECRET_KEY=your-secret-key-change-this-in-production
```

### 3. Run Database Migrations

```bash
# Initialize Alembic (if not already done)
alembic init alembic

# Create initial migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

### 4. Seed Initial Data

```bash
# Create the initial admin user
python scripts/seed_admin.py

# Create default subscription plans
python scripts/seed_plans.py
```

**Admin Login Credentials:**
- Phone: `0746482609`
- Password: `1234567890`

**Default Plans Created:**
- Free Plan: 0 TZS/month (5 products, basic features)
- Basic Plan: 15,000 TZS/month (25 products, chat support)
- Premium Plan: 35,000 TZS/month (100 products, priority listing)
- Enterprise Plan: 75,000 TZS/month (500 products, full features)

### 5. Run the Application

```bash
# Development server with auto-reload
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or using Python directly
python main.py
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Login with form data (phone + password)
- `POST /api/v1/auth/login-json` - Login with JSON payload

### Vendors
- `POST /api/v1/vendors/` - Create vendor account
- `GET /api/v1/vendors/me` - Get current vendor profile
- `PUT /api/v1/vendors/me` - Update current vendor profile
- `DELETE /api/v1/vendors/me` - Delete current vendor account
- `GET /api/v1/vendors/{vendor_id}` - Get vendor by ID
- `PUT /api/v1/vendors/{vendor_id}` - Update vendor (admin only)
- `POST /api/v1/vendors/admin` - Create admin vendor (admin only)

### Categories
- `POST /api/v1/categories/` - Create category (admin only)
- `GET /api/v1/categories/` - Get all categories
- `GET /api/v1/categories/search` - Search categories
- `GET /api/v1/categories/{category_id}` - Get category by ID
- `PUT /api/v1/categories/{category_id}` - Update category (admin only)
- `DELETE /api/v1/categories/{category_id}` - Delete category (admin only)

### Products
- `POST /api/v1/products/` - Create product (vendor/admin)
- `GET /api/v1/products/` - Get products with filtering and pagination
- `GET /api/v1/products/featured` - Get featured products
- `GET /api/v1/products/my-products` - Get current vendor's products
- `GET /api/v1/products/{product_id}` - Get product by ID
- `PUT /api/v1/products/{product_id}` - Update product (owner/admin)
- `DELETE /api/v1/products/{product_id}` - Delete product (owner/admin)

### Customers
- `POST /api/v1/customers/` - Create customer (vendor/admin)
- `GET /api/v1/customers/` - Get customers with search and pagination
- `GET /api/v1/customers/phone/{phone}` - Get customer by phone
- `GET /api/v1/customers/{customer_id}` - Get customer by ID
- `PUT /api/v1/customers/{customer_id}` - Update customer (admin only)
- `DELETE /api/v1/customers/{customer_id}` - Delete customer (admin only)

### Chats
- `POST /api/v1/chats/` - Create chat with customer
- `GET /api/v1/chats/` - Get vendor's chats
- `GET /api/v1/chats/{chat_id}` - Get chat with messages
- `PUT /api/v1/chats/{chat_id}` - Update chat status
- `DELETE /api/v1/chats/{chat_id}` - Delete chat

### Messages
- `POST /api/v1/chats/{chat_id}/messages` - Send message
- `GET /api/v1/chats/{chat_id}/messages` - Get chat messages
- `GET /api/v1/messages/{message_id}` - Get message by ID
- `PUT /api/v1/messages/{message_id}` - Update message (sender only)
- `DELETE /api/v1/messages/{message_id}` - Delete message (sender only)
- `POST /api/v1/chats/{chat_id}/mark-read` - Mark messages as read
- `GET /api/v1/chats/{chat_id}/unread-count` - Get unread message count

### File Uploads
- `POST /api/v1/uploads/single` - Upload single file
- `POST /api/v1/uploads/multiple` - Upload multiple files
- `GET /api/v1/uploads/download` - Download file
- `DELETE /api/v1/uploads/delete` - Delete file
- `GET /api/v1/uploads/info` - Get upload information

### Subscription Plans
- `POST /api/v1/plans/` - Create plan (admin only)
- `GET /api/v1/plans/` - Get all plans
- `GET /api/v1/plans/free` - Get free plan
- `GET /api/v1/plans/{plan_id}` - Get plan by ID
- `PUT /api/v1/plans/{plan_id}` - Update plan (admin only)
- `DELETE /api/v1/plans/{plan_id}` - Delete plan (admin only)

### Subscriptions
- `POST /api/v1/subscriptions/` - Create subscription
- `GET /api/v1/subscriptions/` - Get vendor's subscriptions
- `GET /api/v1/subscriptions/status` - Get subscription status
- `GET /api/v1/subscriptions/active` - Get active subscription
- `GET /api/v1/subscriptions/{subscription_id}` - Get subscription by ID
- `PUT /api/v1/subscriptions/{subscription_id}` - Update subscription
- `POST /api/v1/subscriptions/{subscription_id}/cancel` - Cancel subscription

### In-App Purchases
- `POST /api/v1/in-app-purchases/` - Create in-app purchase (admin only)
- `GET /api/v1/in-app-purchases/` - Get all in-app purchases
- `GET /api/v1/in-app-purchases/{purchase_id}` - Get in-app purchase by ID
- `PUT /api/v1/in-app-purchases/{purchase_id}` - Update in-app purchase (admin only)
- `DELETE /api/v1/in-app-purchases/{purchase_id}` - Delete in-app purchase (admin only)

### Purchases
- `POST /api/v1/purchases/` - Create purchase
- `GET /api/v1/purchases/` - Get vendor's purchases
- `GET /api/v1/purchases/all` - Get all purchases (admin only)
- `GET /api/v1/purchases/{purchase_id}` - Get purchase by ID

### Payments
- `POST /api/v1/payments/` - Create payment
- `GET /api/v1/payments/purchase/{purchase_id}` - Get purchase payments
- `GET /api/v1/payments/{payment_id}` - Get payment by ID
- `PUT /api/v1/payments/{payment_id}/status` - Update payment status (admin only)
- `POST /api/v1/payments/{payment_id}/complete` - Mark payment as completed (admin only)
- `POST /api/v1/payments/{payment_id}/fail` - Mark payment as failed (admin only)

## Database Schema

### Key Tables
- **vendors**: Vendor accounts with phone authentication and roles
- **categories**: Product categories (admin-managed)
- **products**: Product information with images and variants
- **product_images**: Multiple images per product
- **product_variants**: Product variants (size, color, stock tracking)
- **customers**: Customer profiles with phone identification
- **chats**: Vendor-customer conversations with product context
- **messages**: Chat messages with attachments and status tracking
- **plans**: Subscription plans with features and limits
- **subscriptions**: Vendor subscriptions with usage tracking
- **in_app_purchases**: One-time premium feature purchases
- **purchases**: Purchase records for subscriptions and in-app purchases
- **payments**: Payment records with status tracking

### Features
- Soft deletes for data integrity
- UUID primary keys
- Phone number validation for Tanzania (extensible to other countries)
- Role-based access control (vendor/admin)
- File upload system with type validation
- Message status tracking (sent/delivered/read)
- Subscription-based feature access control
- Automated plan expiry with APScheduler
- Usage tracking and limit enforcement
- Scalable payment processing architecture
- Proper indexing for performance
- Timestamps for audit trails

## Development

### Running Tests
```bash
pytest
```

### Code Style
The project follows clean architecture principles with clear separation of concerns:
- **Models**: Database entities
- **Schemas**: Pydantic models for API input/output
- **Repositories**: Data access layer
- **Services**: Business logic layer
- **API**: HTTP endpoints and routing

## Environment Variables

Copy `.env.example` to `.env` and configure:

```env
DATABASE_URL=postgresql://username:password@localhost:5432/dealer_db
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
APP_NAME=Dealer AI Backend
APP_VERSION=1.0.0
DEBUG=True
```

## Next Steps (Future Milestones)

- WhatsApp Business API integration
- AI-powered product recommendations
- Payment gateway integration (MPesa, Selcom)
- File upload to Supabase
- Customer management
- Chat system
- Admin functionality
