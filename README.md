# Dealer AI Backend

A vendor-customer marketplace platform integrated with WhatsApp Business API, AI-powered product recommendations, and Supabase for file storage.

## Features

### Milestone 1 - Core Platform
- **Vendor Management**: Registration, phone-based authentication, profile management
- **Product Management**: CRUD operations with images and variants
- **Category Management**: Product categorization system (admin-only)
- **JWT Authentication**: Secure vendor authentication with phone numbers
- **Role-Based Access Control**: Admin and vendor roles with proper permissions
- **Clean Architecture**: Separation of concerns for scalability
- **Database**: PostgreSQL with SQLAlchemy ORM and Alembic migrations
- **API Documentation**: Automatic Swagger UI documentation

### Milestone 2 - Messaging & Customer Management
- **Customer Management**: Customer profiles with phone-based identification
- **Chat System**: Vendor-customer messaging with product context
- **Message Management**: Send, receive, update, and delete messages
- **File Uploads**: Local file storage for attachments (images, documents, audio, video)
- **Message Status Tracking**: Sent, delivered, read status management
- **Soft Deletes**: Data integrity with soft delete functionality

## Project Structure

```
dealer_backend/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/
│   │       │   ├── auth.py
│   │       │   ├── vendors.py
│   │       │   ├── categories.py
│   │       │   └── products.py
│   │       └── api.py
│   ├── core/
│   │   ├── auth.py
│   │   ├── config.py
│   │   ├── database.py
│   │   └── security.py
│   ├── models/
│   │   ├── vendor.py
│   │   ├── category.py
│   │   └── product.py
│   ├── repositories/
│   │   ├── base.py
│   │   ├── vendor.py
│   │   ├── category.py
│   │   └── product.py
│   ├── schemas/
│   │   ├── vendor.py
│   │   ├── category.py
│   │   └── product.py
│   └── services/
│       ├── vendor.py
│       ├── category.py
│       └── product.py
├── alembic/
│   ├── versions/
│   └── env.py
├── main.py
├── requirements.txt
└── .env
```

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Database Setup

1. Create a PostgreSQL database:
```sql
CREATE DATABASE dealer_db;
```

2. Update the `.env` file with your database credentials:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/dealer_db
SECRET_KEY=your-secret-key-change-this-in-production
```

### 3. Run Database Migrations

```bash
# Initialize Alembic (if not already done)
alembic init alembic

# Create initial migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

### 4. Run the Application

```bash
# Development server with auto-reload
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or using Python directly
python main.py
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Login with form data (phone + password)
- `POST /api/v1/auth/login-json` - Login with JSON payload

### Vendors
- `POST /api/v1/vendors/` - Create vendor account
- `GET /api/v1/vendors/me` - Get current vendor profile
- `PUT /api/v1/vendors/me` - Update current vendor profile
- `DELETE /api/v1/vendors/me` - Delete current vendor account
- `GET /api/v1/vendors/{vendor_id}` - Get vendor by ID
- `PUT /api/v1/vendors/{vendor_id}` - Update vendor (admin only)
- `POST /api/v1/vendors/admin` - Create admin vendor (admin only)

### Categories
- `POST /api/v1/categories/` - Create category (admin only)
- `GET /api/v1/categories/` - Get all categories
- `GET /api/v1/categories/search` - Search categories
- `GET /api/v1/categories/{category_id}` - Get category by ID
- `PUT /api/v1/categories/{category_id}` - Update category (admin only)
- `DELETE /api/v1/categories/{category_id}` - Delete category (admin only)

### Products
- `POST /api/v1/products/` - Create product (vendor/admin)
- `GET /api/v1/products/` - Get products with filtering and pagination
- `GET /api/v1/products/featured` - Get featured products
- `GET /api/v1/products/my-products` - Get current vendor's products
- `GET /api/v1/products/{product_id}` - Get product by ID
- `PUT /api/v1/products/{product_id}` - Update product (owner/admin)
- `DELETE /api/v1/products/{product_id}` - Delete product (owner/admin)

### Customers
- `POST /api/v1/customers/` - Create customer (vendor/admin)
- `GET /api/v1/customers/` - Get customers with search and pagination
- `GET /api/v1/customers/phone/{phone}` - Get customer by phone
- `GET /api/v1/customers/{customer_id}` - Get customer by ID
- `PUT /api/v1/customers/{customer_id}` - Update customer (admin only)
- `DELETE /api/v1/customers/{customer_id}` - Delete customer (admin only)

### Chats
- `POST /api/v1/chats/` - Create chat with customer
- `GET /api/v1/chats/` - Get vendor's chats
- `GET /api/v1/chats/{chat_id}` - Get chat with messages
- `PUT /api/v1/chats/{chat_id}` - Update chat status
- `DELETE /api/v1/chats/{chat_id}` - Delete chat

### Messages
- `POST /api/v1/chats/{chat_id}/messages` - Send message
- `GET /api/v1/chats/{chat_id}/messages` - Get chat messages
- `GET /api/v1/messages/{message_id}` - Get message by ID
- `PUT /api/v1/messages/{message_id}` - Update message (sender only)
- `DELETE /api/v1/messages/{message_id}` - Delete message (sender only)
- `POST /api/v1/chats/{chat_id}/mark-read` - Mark messages as read
- `GET /api/v1/chats/{chat_id}/unread-count` - Get unread message count

### File Uploads
- `POST /api/v1/uploads/single` - Upload single file
- `POST /api/v1/uploads/multiple` - Upload multiple files
- `GET /api/v1/uploads/download` - Download file
- `DELETE /api/v1/uploads/delete` - Delete file
- `GET /api/v1/uploads/info` - Get upload information

## Database Schema

### Key Tables
- **vendors**: Vendor accounts with phone authentication and roles
- **categories**: Product categories (admin-managed)
- **products**: Product information with images and variants
- **product_images**: Multiple images per product
- **product_variants**: Product variants (size, color, stock tracking)
- **customers**: Customer profiles with phone identification
- **chats**: Vendor-customer conversations with product context
- **messages**: Chat messages with attachments and status tracking

### Features
- Soft deletes for data integrity
- UUID primary keys
- Phone number validation for Tanzania (extensible to other countries)
- Role-based access control (vendor/admin)
- File upload system with type validation
- Message status tracking (sent/delivered/read)
- Proper indexing for performance
- Timestamps for audit trails

## Development

### Running Tests
```bash
pytest
```

### Code Style
The project follows clean architecture principles with clear separation of concerns:
- **Models**: Database entities
- **Schemas**: Pydantic models for API input/output
- **Repositories**: Data access layer
- **Services**: Business logic layer
- **API**: HTTP endpoints and routing

## Environment Variables

Copy `.env.example` to `.env` and configure:

```env
DATABASE_URL=postgresql://username:password@localhost:5432/dealer_db
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
APP_NAME=Dealer AI Backend
APP_VERSION=1.0.0
DEBUG=True
```

## Next Steps (Future Milestones)

- WhatsApp Business API integration
- AI-powered product recommendations
- Payment gateway integration (MPesa, Selcom)
- File upload to Supabase
- Customer management
- Chat system
- Admin functionality
