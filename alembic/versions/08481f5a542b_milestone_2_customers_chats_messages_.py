"""Milestone 2: customers,chats,  messages and admin

Revision ID: 08481f5a542b
Revises: ea57528376cb
Create Date: 2025-08-18 12:51:14.951455

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '08481f5a542b'
down_revision = 'ea57528376cb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # # ENUM type for vendorrole 
    # vendorrole_enum = sa.Enum('VENDOR', 'ADMIN', name='vendorrole')
    # vendorrole_enum.create(op.get_bind(), checkfirst=True)

    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customers',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'BLOCKED', name='customerstatus'), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customers_email'), 'customers', ['email'], unique=True)
    op.create_index(op.f('ix_customers_phone'), 'customers', ['phone'], unique=True)
    op.create_table('chats',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('vendor_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('product_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'CLOSED', 'ARCHIVED', name='chatstatus'), nullable=False),
    sa.Column('last_message_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chats_customer_id'), 'chats', ['customer_id'], unique=False)
    op.create_index(op.f('ix_chats_product_id'), 'chats', ['product_id'], unique=False)
    op.create_index(op.f('ix_chats_vendor_id'), 'chats', ['vendor_id'], unique=False)
    op.create_table('messages',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('chat_id', sa.UUID(), nullable=False),
    sa.Column('sender_type', sa.Enum('VENDOR', 'CUSTOMER', name='sendertype'), nullable=False),
    sa.Column('sender_id', sa.UUID(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('status', sa.Enum('SENT', 'DELIVERED', 'READ', 'FAILED', name='messagestatus'), nullable=False),
    sa.Column('attachments', sa.JSON(), nullable=True),
    sa.Column('whatsapp_message_id', sa.String(length=255), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['chat_id'], ['chats.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messages_chat_id'), 'messages', ['chat_id'], unique=False)
    op.create_index(op.f('ix_messages_sender_id'), 'messages', ['sender_id'], unique=False)
    op.create_index(op.f('ix_messages_whatsapp_message_id'), 'messages', ['whatsapp_message_id'], unique=False)
    op.add_column('vendors', sa.Column('role', sa.Enum('VENDOR', 'ADMIN', name='vendorrole'), nullable=False))
    op.alter_column('vendors', 'email',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.create_index(op.f('ix_vendors_phone'), 'vendors', ['phone'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_vendors_phone'), table_name='vendors')
    op.alter_column('vendors', 'email',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.drop_column('vendors', 'role')
    op.drop_index(op.f('ix_messages_whatsapp_message_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_sender_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_chat_id'), table_name='messages')
    op.drop_table('messages')
    op.drop_index(op.f('ix_chats_vendor_id'), table_name='chats')
    op.drop_index(op.f('ix_chats_product_id'), table_name='chats')
    op.drop_index(op.f('ix_chats_customer_id'), table_name='chats')
    op.drop_table('chats')
    op.drop_index(op.f('ix_customers_phone'), table_name='customers')
    op.drop_index(op.f('ix_customers_email'), table_name='customers')
    op.drop_table('customers')
    # ### end Alembic commands ###