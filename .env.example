# Database Configuration
DATABASE_URL=postgresql://kadilana:kadi2004@localhost:5432/dealer_db

# JWT Configuration
SECRET_KEY=63c20e058fd88941e5079df3938d9337abb50020ea484086a08b3e583d3eddb5
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Configuration
APP_NAME=Dealer AI Backend
APP_VERSION=1.0.0
DEBUG=True

# Supabase Configuration (for later milestones)
SUPABASE_URL=https://xbswmvlysukofxunbzhk.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhic3dtdmx5c3Vrb2Z4dW5iemhrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUzNTQ3ODUsImV4cCI6MjA3MDkzMDc4NX0.x0lNjMi8rhFpBvsCA-z_e5-weuYh4hs6D5TChRBeFjM

# WhatsApp API Configuration (for later milestones)
WHATSAPP_API_URL=
WHATSAPP_API_TOKEN=

# AI Configuration (for later milestones)
OPENROUTER_API_KEY=

# Payment Gateway Configuration (for later milestones)
MPESA_CONSUMER_KEY=
MPESA_CONSUMER_SECRET=
SELCOM_API_KEY=
