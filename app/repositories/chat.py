from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, func
from app.models.chat import Chat, Message, SenderType, MessageStatus
from app.repositories.base import BaseRepository


class ChatRepository(BaseRepository[Chat]):
    def __init__(self):
        super().__init__(Chat)

    def get_with_relations(self, db: Session, id: Any) -> Optional[Chat]:
        """Get chat with vendor, customer, product, and messages."""
        return db.query(self.model).options(
            joinedload(self.model.vendor),
            joinedload(self.model.customer),
            joinedload(self.model.product),
            joinedload(self.model.messages)
        ).filter(
            and_(
                self.model.id == id,
                self.model.is_deleted == False
            )
        ).first()

    def get_by_vendor_and_customer(
        self,
        db: Session,
        *,
        vendor_id: str,
        customer_id: str,
        product_id: Optional[str] = None
    ) -> Optional[Chat]:
        """Get chat between vendor and customer for specific product."""
        query = db.query(self.model).filter(
            and_(
                self.model.vendor_id == vendor_id,
                self.model.customer_id == customer_id,
                self.model.is_deleted == False
            )
        )
        
        if product_id:
            query = query.filter(self.model.product_id == product_id)
        
        return query.first()

    def get_vendor_chats(
        self,
        db: Session,
        *,
        vendor_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Chat]:
        """Get all chats for a vendor."""
        filters = {"vendor_id": vendor_id}
        if status:
            filters["status"] = status
        
        return self.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters=filters,
            order_by="last_message_at",
            order_desc=True
        )

    def get_customer_chats(
        self,
        db: Session,
        *,
        customer_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Chat]:
        """Get all chats for a customer."""
        filters = {"customer_id": customer_id}
        if status:
            filters["status"] = status
        
        return self.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters=filters,
            order_by="last_message_at",
            order_desc=True
        )

    def update_last_message_time(self, db: Session, chat_id: str) -> None:
        """Update the last message timestamp for a chat."""
        chat = self.get(db, chat_id)
        if chat:
            chat.last_message_at = func.now()
            db.add(chat)
            db.commit()


class MessageRepository(BaseRepository[Message]):
    def __init__(self):
        super().__init__(Message)

    def get_chat_messages(
        self,
        db: Session,
        *,
        chat_id: str,
        skip: int = 0,
        limit: int = 100,
        order_desc: bool = False
    ) -> List[Message]:
        """Get messages for a specific chat."""
        return db.query(self.model).filter(
            and_(
                self.model.chat_id == chat_id,
                self.model.is_deleted == False
            )
        ).order_by(
            desc(self.model.created_at) if order_desc else self.model.created_at
        ).offset(skip).limit(limit).all()

    def get_unread_count(
        self,
        db: Session,
        *,
        chat_id: str,
        for_sender_type: SenderType,
        sender_id: str
    ) -> int:
        """Get count of unread messages for a specific sender in a chat."""
        return db.query(self.model).filter(
            and_(
                self.model.chat_id == chat_id,
                self.model.sender_type != for_sender_type,
                self.model.sender_id != sender_id,
                self.model.status != MessageStatus.READ,
                self.model.is_deleted == False
            )
        ).count()

    def mark_messages_as_read(
        self,
        db: Session,
        *,
        chat_id: str,
        reader_type: SenderType,
        reader_id: str
    ) -> int:
        """Mark all unread messages in a chat as read for a specific reader."""
        updated_count = db.query(self.model).filter(
            and_(
                self.model.chat_id == chat_id,
                self.model.sender_type != reader_type,
                self.model.sender_id != reader_id,
                self.model.status != MessageStatus.READ,
                self.model.is_deleted == False
            )
        ).update({"status": MessageStatus.READ})
        
        db.commit()
        return updated_count

    def get_latest_message(self, db: Session, *, chat_id: str) -> Optional[Message]:
        """Get the latest message in a chat."""
        return db.query(self.model).filter(
            and_(
                self.model.chat_id == chat_id,
                self.model.is_deleted == False
            )
        ).order_by(desc(self.model.created_at)).first()


# Create repository instances
chat_repository = ChatRepository()
message_repository = MessageRepository()
