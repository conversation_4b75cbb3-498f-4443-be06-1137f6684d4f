from typing import Generic, TypeVar, Type, Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from app.core.database import Base

ModelType = TypeVar("ModelType", bound=Base)


class BaseRepository(Generic[ModelType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model

    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """Get a single record by ID."""
        return db.query(self.model).filter(
            and_(
                self.model.id == id,
                getattr(self.model, 'is_deleted', False) == False
            )
        ).first()

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """Get multiple records with optional filtering and pagination."""
        query = db.query(self.model).filter(
            getattr(self.model, 'is_deleted', False) == False
        )
        
        # Apply filters
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key) and value is not None:
                    query = query.filter(getattr(self.model, key) == value)
        
        # Apply ordering
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        
        return query.offset(skip).limit(limit).all()

    def count(
        self,
        db: Session,
        *,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """Count records with optional filtering."""
        query = db.query(self.model).filter(
            getattr(self.model, 'is_deleted', False) == False
        )
        
        # Apply filters
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key) and value is not None:
                    query = query.filter(getattr(self.model, key) == value)
        
        return query.count()

    def create(self, db: Session, *, obj_in: Dict[str, Any]) -> ModelType:
        """Create a new record."""
        db_obj = self.model(**obj_in)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Dict[str, Any]
    ) -> ModelType:
        """Update an existing record."""
        for field, value in obj_in.items():
            if hasattr(db_obj, field) and value is not None:
                setattr(db_obj, field, value)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def delete(self, db: Session, *, id: Any) -> Optional[ModelType]:
        """Soft delete a record (set is_deleted=True)."""
        obj = self.get(db, id)
        if obj:
            if hasattr(obj, 'is_deleted'):
                obj.is_deleted = True
                db.add(obj)
                db.commit()
                db.refresh(obj)
            else:
                # Hard delete if no soft delete field
                db.delete(obj)
                db.commit()
        return obj

    def get_by_field(
        self,
        db: Session,
        *,
        field: str,
        value: Any
    ) -> Optional[ModelType]:
        """Get a single record by a specific field."""
        if not hasattr(self.model, field):
            return None
        
        return db.query(self.model).filter(
            and_(
                getattr(self.model, field) == value,
                getattr(self.model, 'is_deleted', False) == False
            )
        ).first()
