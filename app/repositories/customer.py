from typing import Optional, List
from sqlalchemy.orm import Session
from app.models.customer import Customer
from app.repositories.base import BaseRepository
from app.core.phone_validation import normalize_phone, CountryCode


class CustomerRepository(BaseRepository[Customer]):
    def __init__(self):
        super().__init__(Customer)

    def get_by_phone(self, db: Session, *, phone: str) -> Optional[Customer]:
        """Get customer by phone number."""
        # Normalize phone number for consistent lookup
        normalized_phone = normalize_phone(phone, CountryCode.TANZANIA)
        if not normalized_phone:
            return None
        return self.get_by_field(db, field="phone", value=normalized_phone)

    def get_by_email(self, db: Session, *, email: str) -> Optional[Customer]:
        """Get customer by email."""
        return self.get_by_field(db, field="email", value=email)

    def create_customer(self, db: Session, *, customer_data: dict) -> Customer:
        """Create a new customer with normalized phone."""
        # Normalize phone number
        if "phone" in customer_data:
            normalized_phone = normalize_phone(customer_data["phone"], CountryCode.TANZANIA)
            if not normalized_phone:
                raise ValueError("Invalid phone number format")
            customer_data["phone"] = normalized_phone
        
        return self.create(db, obj_in=customer_data)

    def search_customers(
        self,
        db: Session,
        *,
        query: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Customer]:
        """Search customers by name or phone."""
        return db.query(self.model).filter(
            self.model.name.ilike(f"%{query}%") | 
            self.model.phone.ilike(f"%{query}%"),
            self.model.is_deleted == False
        ).offset(skip).limit(limit).all()

    def get_active_customers(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[Customer]:
        """Get all active customers."""
        return self.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters={"status": "active"},
            order_by="created_at",
            order_desc=True
        )


# Create repository instance
customer_repository = CustomerRepository()
