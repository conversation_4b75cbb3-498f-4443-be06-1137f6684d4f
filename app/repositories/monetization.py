from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, func
from datetime import datetime, timedelta
from app.models.monetization import (
    Plan, Subscription, InAppPurchase, Purchase, Payment,
    SubscriptionStatus, PaymentStatus, PlanType
)
from app.repositories.base import BaseRepository


class PlanRepository(BaseRepository[Plan]):
    def __init__(self):
        super().__init__(Plan)

    def get_active_plans(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Plan]:
        """Get all active plans."""
        return self.get_multi(
            db, skip=skip, limit=limit, 
            filters={"is_active": True}, 
            order_by="price"
        )

    def get_by_type(self, db: Session, *, plan_type: PlanType) -> List[Plan]:
        """Get plans by type."""
        return self.get_multi(
            db, filters={"plan_type": plan_type, "is_active": True},
            order_by="price"
        )

    def get_free_plan(self, db: Session) -> Optional[Plan]:
        """Get the free plan."""
        return db.query(self.model).filter(
            and_(
                self.model.plan_type == PlanType.FREE,
                self.model.is_active == True,
                self.model.is_deleted == False
            )
        ).first()


class SubscriptionRepository(BaseRepository[Subscription]):
    def __init__(self):
        super().__init__(Subscription)

    def get_with_plan(self, db: Session, id: Any) -> Optional[Subscription]:
        """Get subscription with plan details."""
        return db.query(self.model).options(
            joinedload(self.model.plan)
        ).filter(
            and_(
                self.model.id == id,
                self.model.is_deleted == False
            )
        ).first()

    def get_vendor_active_subscription(self, db: Session, *, vendor_id: str) -> Optional[Subscription]:
        """Get vendor's active subscription."""
        return db.query(self.model).options(
            joinedload(self.model.plan)
        ).filter(
            and_(
                self.model.vendor_id == vendor_id,
                self.model.status == SubscriptionStatus.ACTIVE,
                self.model.end_date > func.now(),
                self.model.is_deleted == False
            )
        ).first()

    def get_vendor_subscriptions(
        self, 
        db: Session, 
        *, 
        vendor_id: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Subscription]:
        """Get all subscriptions for a vendor."""
        return db.query(self.model).options(
            joinedload(self.model.plan)
        ).filter(
            and_(
                self.model.vendor_id == vendor_id,
                self.model.is_deleted == False
            )
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_expiring_subscriptions(
        self, 
        db: Session, 
        *, 
        days_ahead: int = 7
    ) -> List[Subscription]:
        """Get subscriptions expiring in the next N days."""
        expiry_date = datetime.utcnow() + timedelta(days=days_ahead)
        return db.query(self.model).options(
            joinedload(self.model.plan),
            joinedload(self.model.vendor)
        ).filter(
            and_(
                self.model.status == SubscriptionStatus.ACTIVE,
                self.model.end_date <= expiry_date,
                self.model.end_date > func.now(),
                self.model.is_deleted == False
            )
        ).all()

    def get_expired_subscriptions(self, db: Session) -> List[Subscription]:
        """Get subscriptions that have expired but status is still active."""
        return db.query(self.model).filter(
            and_(
                self.model.status == SubscriptionStatus.ACTIVE,
                self.model.end_date <= func.now(),
                self.model.is_deleted == False
            )
        ).all()

    def update_usage_stats(
        self, 
        db: Session, 
        *, 
        subscription_id: str, 
        products_count: int = None,
        file_usage_mb: float = None
    ) -> Optional[Subscription]:
        """Update subscription usage statistics."""
        subscription = self.get(db, subscription_id)
        if not subscription:
            return None

        if products_count is not None:
            subscription.current_products_count = products_count
        if file_usage_mb is not None:
            subscription.current_file_usage_mb = file_usage_mb

        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        return subscription


class InAppPurchaseRepository(BaseRepository[InAppPurchase]):
    def __init__(self):
        super().__init__(InAppPurchase)

    def get_active_purchases(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[InAppPurchase]:
        """Get all active in-app purchases."""
        return self.get_multi(
            db, skip=skip, limit=limit,
            filters={"is_active": True},
            order_by="price"
        )


class PurchaseRepository(BaseRepository[Purchase]):
    def __init__(self):
        super().__init__(Purchase)

    def get_with_relations(self, db: Session, id: Any) -> Optional[Purchase]:
        """Get purchase with related data."""
        return db.query(self.model).options(
            joinedload(self.model.subscription).joinedload(Subscription.plan),
            joinedload(self.model.in_app_purchase),
            joinedload(self.model.payments)
        ).filter(
            and_(
                self.model.id == id,
                self.model.is_deleted == False
            )
        ).first()

    def get_vendor_purchases(
        self,
        db: Session,
        *,
        vendor_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Purchase]:
        """Get all purchases for a vendor."""
        return db.query(self.model).options(
            joinedload(self.model.subscription).joinedload(Subscription.plan),
            joinedload(self.model.in_app_purchase),
            joinedload(self.model.payments)
        ).filter(
            and_(
                self.model.vendor_id == vendor_id,
                self.model.is_deleted == False
            )
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_pending_purchases(self, db: Session) -> List[Purchase]:
        """Get purchases with pending payments."""
        return db.query(self.model).join(Payment).filter(
            and_(
                Payment.status == PaymentStatus.PENDING,
                self.model.is_deleted == False
            )
        ).all()


class PaymentRepository(BaseRepository[Payment]):
    def __init__(self):
        super().__init__(Payment)

    def get_by_external_reference(self, db: Session, *, reference: str) -> Optional[Payment]:
        """Get payment by external reference."""
        return db.query(self.model).filter(
            and_(
                self.model.external_reference == reference,
                self.model.is_deleted == False
            )
        ).first()

    def get_purchase_payments(
        self,
        db: Session,
        *,
        purchase_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """Get all payments for a purchase."""
        return db.query(self.model).filter(
            and_(
                self.model.purchase_id == purchase_id,
                self.model.is_deleted == False
            )
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_pending_payments(self, db: Session) -> List[Payment]:
        """Get all pending payments."""
        return db.query(self.model).filter(
            and_(
                self.model.status == PaymentStatus.PENDING,
                self.model.is_deleted == False
            )
        ).order_by(self.model.created_at).all()

    def get_failed_payments(
        self,
        db: Session,
        *,
        hours_ago: int = 24
    ) -> List[Payment]:
        """Get payments that failed in the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_ago)
        return db.query(self.model).filter(
            and_(
                self.model.status == PaymentStatus.FAILED,
                self.model.failed_at >= cutoff_time,
                self.model.is_deleted == False
            )
        ).all()

    def update_payment_status(
        self,
        db: Session,
        *,
        payment_id: str,
        status: PaymentStatus,
        external_reference: str = None,
        gateway_response: dict = None,
        admin_notes: str = None,
        processed_by: str = None
    ) -> Optional[Payment]:
        """Update payment status and related fields."""
        payment = self.get(db, payment_id)
        if not payment:
            return None

        payment.status = status
        if external_reference:
            payment.external_reference = external_reference
        if gateway_response:
            payment.gateway_response = gateway_response
        if admin_notes:
            payment.admin_notes = admin_notes
        if processed_by:
            payment.processed_by = processed_by

        # Set timestamps based on status
        if status == PaymentStatus.COMPLETED:
            payment.paid_at = func.now()
        elif status == PaymentStatus.FAILED:
            payment.failed_at = func.now()

        db.add(payment)
        db.commit()
        db.refresh(payment)
        return payment


# Create repository instances
plan_repository = PlanRepository()
subscription_repository = SubscriptionRepository()
in_app_purchase_repository = InAppPurchaseRepository()
purchase_repository = PurchaseRepository()
payment_repository = PaymentRepository()
