from typing import Optional
from sqlalchemy.orm import Session
from app.models.vendor import Vendor
from app.repositories.base import BaseRepository
from app.core.security import get_password_hash, verify_password


class VendorRepository(BaseRepository[Vendor]):
    def __init__(self):
        super().__init__(Vendor)

    def get_by_phone(self, db: Session, *, phone: str) -> Optional[Vendor]:
        """Get vendor by phone."""
        return self.get_by_field(db, field="phone", value=phone)

    def create_vendor(self, db: Session, *, vendor_data: dict) -> Vendor:
        """Create a new vendor with hashed password."""
        # Hash the password before storing
        if "password" in vendor_data:
            vendor_data["password_hash"] = get_password_hash(vendor_data.pop("password"))
        
        return self.create(db, obj_in=vendor_data)

    def authenticate(self, db: Session, *, phone: str, password: str) -> Optional[Vendor]:
        """Authenticate vendor by phone and password."""
        vendor = self.get_by_phone(db, phone=phone)
        if not vendor:
            return None
        if not verify_password(password, vendor.password_hash):
            return None
        return vendor

    def is_active(self, vendor: Vendor) -> bool:
        """Check if vendor is active."""
        return vendor.status.value == "active" and not vendor.is_deleted


# Create repository instance
vendor_repository = VendorRepository()
