from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, asc
from app.models.product import Product, ProductImage, ProductVariant
from app.repositories.base import BaseRepository


class ProductRepository(BaseRepository[Product]):
    def __init__(self):
        super().__init__(Product)

    def get_with_relations(self, db: Session, id: Any) -> Optional[Product]:
        """Get product with images and variants."""
        return db.query(self.model).options(
            joinedload(self.model.images),
            joinedload(self.model.variants),
            joinedload(self.model.vendor),
            joinedload(self.model.category)
        ).filter(
            and_(
                self.model.id == id,
                self.model.is_deleted == False
            )
        ).first()

    def get_by_vendor(
        self,
        db: Session,
        *,
        vendor_id: str,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Product]:
        """Get products by vendor with optional filtering."""
        base_filters = {"vendor_id": vendor_id}
        if filters:
            base_filters.update(filters)
        
        return self.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters=base_filters,
            order_by="created_at",
            order_desc=True
        )

    def get_by_category(
        self,
        db: Session,
        *,
        category_id: str,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Product]:
        """Get products by category with optional filtering."""
        base_filters = {"category_id": category_id}
        if filters:
            base_filters.update(filters)
        
        return self.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters=base_filters,
            order_by="created_at",
            order_desc=True
        )

    def search_products(
        self,
        db: Session,
        *,
        query: str,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Product]:
        """Search products by name or description."""
        db_query = db.query(self.model).filter(
            and_(
                or_(
                    self.model.name.ilike(f"%{query}%"),
                    self.model.description.ilike(f"%{query}%")
                ),
                self.model.is_deleted == False
            )
        )
        
        # Apply additional filters
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key) and value is not None:
                    db_query = db_query.filter(getattr(self.model, key) == value)
        
        return db_query.offset(skip).limit(limit).all()

    def get_featured_products(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[Product]:
        """Get featured products."""
        return self.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters={"is_featured": True, "status": "active"},
            order_by="created_at",
            order_desc=True
        )

    def create_with_relations(
        self,
        db: Session,
        *,
        product_data: dict,
        images: List[dict] = None,
        variants: List[dict] = None
    ) -> Product:
        """Create product with images and variants."""
        # Create the product first
        product = self.create(db, obj_in=product_data)
        
        # Add images if provided
        if images:
            for image_data in images:
                image_data["product_id"] = product.id
                db_image = ProductImage(**image_data)
                db.add(db_image)
        
        # Add variants if provided
        if variants:
            for variant_data in variants:
                variant_data["product_id"] = product.id
                db_variant = ProductVariant(**variant_data)
                db.add(db_variant)
        
        db.commit()
        db.refresh(product)
        return product


class ProductImageRepository(BaseRepository[ProductImage]):
    def __init__(self):
        super().__init__(ProductImage)

    def get_by_product(self, db: Session, *, product_id: str) -> List[ProductImage]:
        """Get all images for a product."""
        return db.query(self.model).filter(
            self.model.product_id == product_id
        ).order_by(desc(self.model.is_primary), self.model.created_at).all()

    def get_primary_image(self, db: Session, *, product_id: str) -> Optional[ProductImage]:
        """Get primary image for a product."""
        return db.query(self.model).filter(
            and_(
                self.model.product_id == product_id,
                self.model.is_primary == True
            )
        ).first()


class ProductVariantRepository(BaseRepository[ProductVariant]):
    def __init__(self):
        super().__init__(ProductVariant)

    def get_by_product(self, db: Session, *, product_id: str) -> List[ProductVariant]:
        """Get all variants for a product."""
        return db.query(self.model).filter(
            and_(
                self.model.product_id == product_id,
                self.model.is_active == True
            )
        ).order_by(self.model.name, self.model.value).all()


# Create repository instances
product_repository = ProductRepository()
product_image_repository = ProductImageRepository()
product_variant_repository = ProductVariantRepository()
