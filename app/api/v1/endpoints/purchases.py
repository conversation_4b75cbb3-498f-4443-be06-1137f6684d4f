from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.core.permissions import require_admin
from app.schemas.monetization import Purchase<PERSON><PERSON>, PurchaseResponse, PurchaseListResponse
from app.services.monetization import purchase_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=PurchaseResponse, status_code=status.HTTP_201_CREATED)
async def create_purchase(
    purchase_data: PurchaseCreate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Create a new purchase for the current vendor.
    """
    return purchase_service.create_purchase(db, purchase_data, str(current_vendor.id))


@router.get("/", response_model=PurchaseListResponse)
async def get_my_purchases(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get all purchases for the current vendor.
    """
    return purchase_service.get_vendor_purchases(
        db, str(current_vendor.id), skip=skip, limit=limit
    )


@router.get("/all", response_model=PurchaseListResponse)
async def get_all_purchases(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    vendor_id: str = Query(None, description="Filter by vendor ID"),
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Get all purchases (admin only).
    """
    if vendor_id:
        return purchase_service.get_vendor_purchases(
            db, vendor_id, skip=skip, limit=limit
        )
    
    # For admin, we would need to implement get_all_purchases in the service
    # For now, return empty list
    return PurchaseListResponse(
        purchases=[],
        total=0,
        page=1,
        size=limit,
        pages=0
    )


@router.get("/{purchase_id}", response_model=PurchaseResponse)
async def get_purchase(
    purchase_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get purchase by ID (only own purchases).
    """
    purchase = purchase_service.get_purchase(db, purchase_id)
    
    if not purchase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Purchase not found"
        )
    
    # Check if purchase belongs to current vendor (unless admin)
    if (str(purchase.vendor_id) != str(current_vendor.id) and 
        current_vendor.role.value != "admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return purchase
