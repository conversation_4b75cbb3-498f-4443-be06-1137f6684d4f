from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.permissions import require_admin, require_vendor_or_admin
from app.schemas.customer import CustomerCreate, CustomerUpdate, CustomerResponse, CustomerListResponse
from app.services.customer import customer_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=CustomerResponse, status_code=status.HTTP_201_CREATED)
async def create_customer(
    customer_data: CustomerCreate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_vendor_or_admin)
):
    """
    Create a new customer (vendors and admins only).
    """
    return customer_service.create_customer(db, customer_data)


@router.get("/", response_model=CustomerListResponse)
async def get_customers(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, min_length=1, description="Search by name or phone"),
    active_only: bool = Query(True, description="Return only active customers"),
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_vendor_or_admin)
):
    """
    Get all customers with pagination and search (vendors and admins only).
    """
    return customer_service.get_customers(
        db, skip=skip, limit=limit, search=search, active_only=active_only
    )


@router.get("/phone/{phone}", response_model=CustomerResponse)
async def get_customer_by_phone(
    phone: str,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_vendor_or_admin)
):
    """
    Get customer by phone number (vendors and admins only).
    """
    customer = customer_service.get_customer_by_phone(db, phone)
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    return customer


@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: str,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_vendor_or_admin)
):
    """
    Get customer by ID (vendors and admins only).
    """
    customer = customer_service.get_customer(db, customer_id)
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    return customer


@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: str,
    customer_data: CustomerUpdate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)  # Only admins can update customers
):
    """
    Update customer (admins only).
    """
    updated_customer = customer_service.update_customer(
        db, customer_id, customer_data
    )
    
    if not updated_customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    return updated_customer


@router.delete("/{customer_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_customer(
    customer_id: str,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)  # Only admins can delete customers
):
    """
    Delete customer (admins only).
    """
    success = customer_service.delete_customer(db, customer_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
