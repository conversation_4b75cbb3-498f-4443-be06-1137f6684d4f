from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.core.subscription_middleware import get_subscription_info
from app.schemas.monetization import (
    SubscriptionCreate, SubscriptionUpdate, SubscriptionResponse, 
    SubscriptionListResponse, SubscriptionStatusResponse
)
from app.services.monetization import subscription_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=SubscriptionResponse, status_code=status.HTTP_201_CREATED)
async def create_subscription(
    subscription_data: SubscriptionCreate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Create a new subscription for the current vendor.
    """
    return subscription_service.create_subscription(db, subscription_data, str(current_vendor.id))


@router.get("/", response_model=SubscriptionListResponse)
async def get_my_subscriptions(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get all subscriptions for the current vendor.
    """
    return subscription_service.get_vendor_subscriptions(
        db, str(current_vendor.id), skip=skip, limit=limit
    )


@router.get("/status", response_model=SubscriptionStatusResponse)
async def get_subscription_status(
    subscription_status: SubscriptionStatusResponse = Depends(get_subscription_info)
):
    """
    Get current subscription status and usage information.
    """
    return subscription_status


@router.get("/active", response_model=SubscriptionResponse)
async def get_active_subscription(
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get the current active subscription.
    """
    subscription = subscription_service.get_vendor_active_subscription(db, str(current_vendor.id))
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active subscription found"
        )
    
    return subscription


@router.get("/{subscription_id}", response_model=SubscriptionResponse)
async def get_subscription(
    subscription_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get subscription by ID (only own subscriptions).
    """
    subscription = subscription_service.get_subscription(db, subscription_id)
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )
    
    # Check if subscription belongs to current vendor
    if str(subscription.vendor_id) != str(current_vendor.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return subscription


@router.put("/{subscription_id}", response_model=SubscriptionResponse)
async def update_subscription(
    subscription_id: str,
    subscription_data: SubscriptionUpdate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Update subscription (only own subscriptions).
    """
    # First check if subscription exists and belongs to vendor
    subscription = subscription_service.get_subscription(db, subscription_id)
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )
    
    if str(subscription.vendor_id) != str(current_vendor.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    updated_subscription = subscription_service.update_subscription(
        db, subscription_id, subscription_data
    )
    
    return updated_subscription


@router.post("/{subscription_id}/cancel", response_model=SubscriptionResponse)
async def cancel_subscription(
    subscription_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Cancel subscription (only own subscriptions).
    """
    # First check if subscription exists and belongs to vendor
    subscription = subscription_service.get_subscription(db, subscription_id)
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )
    
    if str(subscription.vendor_id) != str(current_vendor.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    cancelled_subscription = subscription_service.cancel_subscription(db, subscription_id)
    
    return cancelled_subscription
