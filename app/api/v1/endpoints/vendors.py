from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.core.permissions import require_admin
from app.schemas.vendor import VendorCreate, VendorUpdate, VendorResponse
from app.services.vendor import vendor_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=VendorResponse, status_code=status.HTTP_201_CREATED)
async def create_vendor(
    vendor_data: VendorCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new vendor account.
    """
    return vendor_service.create_vendor(db, vendor_data)


@router.get("/me", response_model=VendorResponse)
async def get_current_vendor_profile(
    current_vendor: Vendor = Depends(get_current_active_vendor)
):
    """
    Get current vendor's profile.
    """
    return VendorResponse.model_validate(current_vendor)


@router.put("/me", response_model=VendorResponse)
async def update_current_vendor_profile(
    vendor_data: VendorUpdate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Update current vendor's profile.
    """
    updated_vendor = vendor_service.update_vendor(
        db, str(current_vendor.id), vendor_data, current_vendor
    )

    if not updated_vendor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vendor not found"
        )

    return updated_vendor


@router.delete("/me", status_code=status.HTTP_204_NO_CONTENT)
async def delete_current_vendor_account(
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Delete current vendor's account (soft delete).
    """
    success = vendor_service.delete_vendor(db, str(current_vendor.id))
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vendor not found"
        )


@router.get("/{vendor_id}", response_model=VendorResponse)
async def get_vendor_by_id(
    vendor_id: str,
    db: Session = Depends(get_db)
):
    """
    Get vendor by ID (public endpoint).
    """
    vendor = vendor_service.get_vendor(db, vendor_id)
    
    if not vendor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vendor not found"
        )
    
    return vendor


@router.put("/{vendor_id}", response_model=VendorResponse)
async def update_vendor_by_admin(
    vendor_id: str,
    vendor_data: VendorUpdate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Update any vendor (admin only).
    """
    updated_vendor = vendor_service.update_vendor(
        db, vendor_id, vendor_data, current_vendor
    )

    if not updated_vendor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vendor not found"
        )

    return updated_vendor


@router.post("/admin", response_model=VendorResponse, status_code=status.HTTP_201_CREATED)
async def create_admin_vendor(
    vendor_data: VendorCreate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Create a new admin vendor (admin only).
    """
    return vendor_service.create_admin_vendor(db, vendor_data)
