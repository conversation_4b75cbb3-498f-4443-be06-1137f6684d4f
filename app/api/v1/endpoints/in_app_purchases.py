from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.permissions import require_admin
from app.schemas.monetization import (
    InAppPurchaseCreate, InAppPurchaseUpdate, InAppPurchaseResponse, InAppPurchaseListResponse
)
from app.services.monetization import in_app_purchase_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=InAppPurchaseResponse, status_code=status.HTTP_201_CREATED)
async def create_in_app_purchase(
    purchase_data: InAppPurchaseCreate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Create a new in-app purchase (admin only).
    """
    return in_app_purchase_service.create_in_app_purchase(db, purchase_data)


@router.get("/", response_model=InAppPurchaseListResponse)
async def get_in_app_purchases(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    active_only: bool = Query(True, description="Return only active purchases"),
    db: Session = Depends(get_db)
):
    """
    Get all in-app purchases (public endpoint).
    """
    return in_app_purchase_service.get_in_app_purchases(
        db, skip=skip, limit=limit, active_only=active_only
    )


@router.get("/{purchase_id}", response_model=InAppPurchaseResponse)
async def get_in_app_purchase(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """
    Get in-app purchase by ID (public endpoint).
    """
    purchase = in_app_purchase_service.get_in_app_purchase(db, purchase_id)
    
    if not purchase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="In-app purchase not found"
        )
    
    return purchase


@router.put("/{purchase_id}", response_model=InAppPurchaseResponse)
async def update_in_app_purchase(
    purchase_id: str,
    purchase_data: InAppPurchaseUpdate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Update in-app purchase (admin only).
    """
    updated_purchase = in_app_purchase_service.update_in_app_purchase(
        db, purchase_id, purchase_data
    )
    
    if not updated_purchase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="In-app purchase not found"
        )
    
    return updated_purchase


@router.delete("/{purchase_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_in_app_purchase(
    purchase_id: str,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Delete in-app purchase (admin only).
    """
    success = in_app_purchase_service.delete_in_app_purchase(db, purchase_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="In-app purchase not found"
        )
