from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.core.permissions import require_admin
from app.schemas.monetization import PaymentCreate, PaymentUpdate, PaymentResponse, PaymentListResponse
from app.services.monetization import payment_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=PaymentResponse, status_code=status.HTTP_201_CREATED)
async def create_payment(
    payment_data: PaymentCreate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Create a new payment for a purchase.
    """
    return payment_service.create_payment(db, payment_data)


@router.get("/purchase/{purchase_id}", response_model=PaymentListResponse)
async def get_purchase_payments(
    purchase_id: str,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get all payments for a specific purchase.
    """
    # First verify the purchase exists and belongs to vendor
    from app.services.monetization import purchase_service
    purchase = purchase_service.get_purchase(db, purchase_id)
    
    if not purchase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Purchase not found"
        )
    
    # Check if purchase belongs to current vendor (unless admin)
    if (str(purchase.vendor_id) != str(current_vendor.id) and 
        current_vendor.role.value != "admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return payment_service.get_purchase_payments(
        db, purchase_id, skip=skip, limit=limit
    )


@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get payment by ID.
    """
    payment = payment_service.get_payment(db, payment_id)
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    # Verify access through purchase ownership
    from app.services.monetization import purchase_service
    purchase = purchase_service.get_purchase(db, str(payment.purchase_id))
    
    if not purchase:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Associated purchase not found"
        )
    
    # Check if purchase belongs to current vendor (unless admin)
    if (str(purchase.vendor_id) != str(current_vendor.id) and 
        current_vendor.role.value != "admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return payment


@router.put("/{payment_id}/status", response_model=PaymentResponse)
async def update_payment_status(
    payment_id: str,
    payment_data: PaymentUpdate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Update payment status (admin only).
    """
    updated_payment = payment_service.update_payment_status(
        db, payment_id, payment_data, str(current_vendor.id)
    )
    
    if not updated_payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    return updated_payment


@router.post("/{payment_id}/complete", response_model=PaymentResponse)
async def complete_payment(
    payment_id: str,
    external_reference: str = Query(None, description="External payment reference"),
    admin_notes: str = Query(None, description="Admin notes"),
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Mark payment as completed (admin only).
    """
    from app.models.monetization import PaymentStatus
    
    payment_update = PaymentUpdate(
        status=PaymentStatus.COMPLETED,
        external_reference=external_reference,
        admin_notes=admin_notes
    )
    
    updated_payment = payment_service.update_payment_status(
        db, payment_id, payment_update, str(current_vendor.id)
    )
    
    if not updated_payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    return updated_payment


@router.post("/{payment_id}/fail", response_model=PaymentResponse)
async def fail_payment(
    payment_id: str,
    admin_notes: str = Query(None, description="Reason for failure"),
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Mark payment as failed (admin only).
    """
    from app.models.monetization import PaymentStatus
    
    payment_update = PaymentUpdate(
        status=PaymentStatus.FAILED,
        admin_notes=admin_notes
    )
    
    updated_payment = payment_service.update_payment_status(
        db, payment_id, payment_update, str(current_vendor.id)
    )
    
    if not updated_payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    return updated_payment
