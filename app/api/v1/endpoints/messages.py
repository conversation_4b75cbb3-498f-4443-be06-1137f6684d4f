from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.schemas.chat import MessageCreate, MessageUpdate, MessageResponse, MessageListResponse
from app.services.message import message_service
from app.models.vendor import Vendor
from app.models.chat import SenderType

router = APIRouter()


@router.post("/chats/{chat_id}/messages", response_model=MessageResponse, status_code=status.HTTP_201_CREATED)
async def send_message(
    chat_id: str,
    message_data: MessageCreate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Send a message in a chat.
    """
    return message_service.send_message(
        db, chat_id, message_data, str(current_vendor.id), SenderType.VENDOR
    )


@router.get("/chats/{chat_id}/messages", response_model=MessageListResponse)
async def get_chat_messages(
    chat_id: str,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    order_desc: bool = Query(False, description="Order messages in descending order (newest first)"),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get messages for a chat.
    """
    return message_service.get_chat_messages(
        db, chat_id, str(current_vendor.id), SenderType.VENDOR,
        skip=skip, limit=limit, order_desc=order_desc
    )


@router.get("/messages/{message_id}", response_model=MessageResponse)
async def get_message(
    message_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get message by ID.
    """
    message = message_service.get_message(db, message_id)
    
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )
    
    return message


@router.put("/messages/{message_id}", response_model=MessageResponse)
async def update_message(
    message_id: str,
    message_data: MessageUpdate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Update message (only sender can update).
    """
    updated_message = message_service.update_message(
        db, message_id, message_data, str(current_vendor.id), SenderType.VENDOR
    )
    
    if not updated_message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )
    
    return updated_message


@router.delete("/messages/{message_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_message(
    message_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Delete message (only sender can delete).
    """
    success = message_service.delete_message(
        db, message_id, str(current_vendor.id), SenderType.VENDOR
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )


@router.post("/chats/{chat_id}/mark-read", response_model=dict)
async def mark_messages_as_read(
    chat_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Mark all unread messages in a chat as read.
    """
    count = message_service.mark_messages_as_read(
        db, chat_id, str(current_vendor.id), SenderType.VENDOR
    )
    
    return {"marked_as_read": count}


@router.get("/chats/{chat_id}/unread-count", response_model=dict)
async def get_unread_count(
    chat_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get count of unread messages for vendor in a chat.
    """
    count = message_service.get_unread_count(
        db, chat_id, str(current_vendor.id), SenderType.VENDOR
    )
    
    return {"unread_count": count}
