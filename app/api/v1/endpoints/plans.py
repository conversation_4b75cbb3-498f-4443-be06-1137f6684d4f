from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.permissions import require_admin
from app.schemas.monetization import PlanCreate, PlanUpdate, PlanResponse, PlanListResponse
from app.services.monetization import plan_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=PlanResponse, status_code=status.HTTP_201_CREATED)
async def create_plan(
    plan_data: PlanCreate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Create a new subscription plan (admin only).
    """
    return plan_service.create_plan(db, plan_data)


@router.get("/", response_model=PlanListResponse)
async def get_plans(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    active_only: bool = Query(True, description="Return only active plans"),
    db: Session = Depends(get_db)
):
    """
    Get all subscription plans (public endpoint).
    """
    return plan_service.get_plans(db, skip=skip, limit=limit, active_only=active_only)


@router.get("/free", response_model=PlanResponse)
async def get_free_plan(
    db: Session = Depends(get_db)
):
    """
    Get the free plan (public endpoint).
    """
    free_plan = plan_service.get_free_plan(db)
    
    if not free_plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Free plan not found"
        )
    
    return free_plan


@router.get("/{plan_id}", response_model=PlanResponse)
async def get_plan(
    plan_id: str,
    db: Session = Depends(get_db)
):
    """
    Get plan by ID (public endpoint).
    """
    plan = plan_service.get_plan(db, plan_id)
    
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    return plan


@router.put("/{plan_id}", response_model=PlanResponse)
async def update_plan(
    plan_id: str,
    plan_data: PlanUpdate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Update plan (admin only).
    """
    updated_plan = plan_service.update_plan(db, plan_id, plan_data)
    
    if not updated_plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    return updated_plan


@router.delete("/{plan_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(require_admin)
):
    """
    Delete plan (admin only).
    """
    success = plan_service.delete_plan(db, plan_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
