from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.schemas.vendor import Vend<PERSON><PERSON><PERSON><PERSON>, Token, VendorResponse
from app.services.vendor import vendor_service

router = APIRouter()


@router.post("/login", response_model=dict)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Login vendor and return access token.
    Note: Use phone number as username in the form.
    """
    result = vendor_service.authenticate_vendor(
        db, phone=form_data.username, password=form_data.password
    )
    
    if not result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect phone number or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return result


@router.post("/login-json", response_model=dict)
async def login_json(
    login_data: VendorLogin,
    db: Session = Depends(get_db)
):
    """
    Login vendor with JSON payload and return access token.
    """
    result = vendor_service.authenticate_vendor(
        db, phone=login_data.phone, password=login_data.password
    )
    
    if not result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect phone number or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return result
