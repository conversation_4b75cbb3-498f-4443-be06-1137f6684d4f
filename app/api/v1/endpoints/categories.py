from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.schemas.category import CategoryCreate, CategoryUpdate, CategoryResponse
from app.services.category import category_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=CategoryResponse, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_data: CategoryCreate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(get_current_active_vendor)
):
    """
    Create a new category (requires authentication).
    """
    return category_service.create_category(db, category_data)


@router.get("/", response_model=List[CategoryResponse])
async def get_categories(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    active_only: bool = Query(True, description="Return only active categories"),
    db: Session = Depends(get_db)
):
    """
    Get all categories with pagination.
    """
    return category_service.get_categories(
        db, skip=skip, limit=limit, active_only=active_only
    )


@router.get("/search", response_model=List[CategoryResponse])
async def search_categories(
    name: str = Query(..., min_length=1, description="Category name to search for"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """
    Search categories by name.
    """
    return category_service.search_categories(
        db, name=name, skip=skip, limit=limit
    )


@router.get("/{category_id}", response_model=CategoryResponse)
async def get_category(
    category_id: str,
    db: Session = Depends(get_db)
):
    """
    Get category by ID.
    """
    category = category_service.get_category(db, category_id)
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return category


@router.put("/{category_id}", response_model=CategoryResponse)
async def update_category(
    category_id: str,
    category_data: CategoryUpdate,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(get_current_active_vendor)
):
    """
    Update category (requires authentication).
    """
    updated_category = category_service.update_category(
        db, category_id, category_data
    )
    
    if not updated_category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return updated_category


@router.delete("/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_category(
    category_id: str,
    db: Session = Depends(get_db),
    current_vendor: Vendor = Depends(get_current_active_vendor)
):
    """
    Delete category (requires authentication).
    """
    success = category_service.delete_category(db, category_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
