from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor, get_optional_current_vendor
from app.schemas.product import (
    ProductCreate, ProductUpdate, ProductResponse, 
    ProductListResponse, ProductsListResponse
)
from app.services.product import product_service
from app.models.vendor import Vendor

router = APIRouter()


@router.post("/", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(
    product_data: ProductCreate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Create a new product.
    """
    return product_service.create_product(db, product_data, str(current_vendor.id))


@router.get("/", response_model=ProductsListResponse)
async def get_products(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    vendor_id: Optional[str] = Query(None, description="Filter by vendor ID"),
    category_id: Optional[str] = Query(None, description="Filter by category ID"),
    status: Optional[str] = Query(None, description="Filter by product status"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    search: Optional[str] = Query(None, min_length=1, description="Search in product name and description"),
    order_by: str = Query("created_at", description="Field to order by"),
    order_desc: bool = Query(True, description="Order in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get products with filtering, pagination, and search.
    """
    return product_service.get_products(
        db=db,
        skip=skip,
        limit=limit,
        vendor_id=vendor_id,
        category_id=category_id,
        status=status,
        is_featured=is_featured,
        search=search,
        order_by=order_by,
        order_desc=order_desc
    )


@router.get("/featured", response_model=List[ProductListResponse])
async def get_featured_products(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """
    Get featured products.
    """
    return product_service.get_featured_products(db, skip=skip, limit=limit)


@router.get("/my-products", response_model=ProductsListResponse)
async def get_my_products(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    status: Optional[str] = Query(None, description="Filter by product status"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    search: Optional[str] = Query(None, min_length=1, description="Search in product name and description"),
    order_by: str = Query("created_at", description="Field to order by"),
    order_desc: bool = Query(True, description="Order in descending order"),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get current vendor's products.
    """
    return product_service.get_products(
        db=db,
        skip=skip,
        limit=limit,
        vendor_id=str(current_vendor.id),
        status=status,
        is_featured=is_featured,
        search=search,
        order_by=order_by,
        order_desc=order_desc
    )


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: str,
    db: Session = Depends(get_db)
):
    """
    Get product by ID.
    """
    product = product_service.get_product(db, product_id)
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    
    return product


@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: str,
    product_data: ProductUpdate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Update product.
    """
    updated_product = product_service.update_product(
        db, product_id, product_data, str(current_vendor.id)
    )
    
    if not updated_product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    
    return updated_product


@router.delete("/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(
    product_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Delete product.
    """
    success = product_service.delete_product(db, product_id, str(current_vendor.id))
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
