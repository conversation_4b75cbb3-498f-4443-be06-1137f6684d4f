from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.core.file_storage import file_storage
from app.models.vendor import Vendor
import os

router = APIRouter()


@router.post("/single", response_model=dict)
async def upload_single_file(
    file: UploadFile = File(...),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Upload a single file.
    """
    file_info = await file_storage.save_file(file)
    
    return {
        "message": "File uploaded successfully",
        "file": file_info
    }


@router.post("/multiple", response_model=dict)
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Upload multiple files (max 10).
    """
    files_info = await file_storage.save_multiple_files(files)
    
    return {
        "message": f"{len(files_info)} files uploaded successfully",
        "files": files_info
    }


@router.get("/download")
async def download_file(
    file_url: str,
    current_vendor: Vendor = Depends(get_current_active_vendor)
):
    """
    Download a file by its URL.
    """
    file_path = file_storage.get_file_path(file_url)
    
    if not file_path or not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    return FileResponse(
        path=str(file_path),
        filename=file_path.name,
        media_type='application/octet-stream'
    )


@router.delete("/delete")
async def delete_file(
    file_url: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Delete a file by its URL.
    Note: This should be used carefully as it permanently deletes the file.
    """
    success = file_storage.delete_file(file_url)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found or could not be deleted"
        )
    
    return {"message": "File deleted successfully"}


@router.get("/info")
async def get_upload_info():
    """
    Get information about allowed file types and sizes.
    """
    return {
        "allowed_types": file_storage.ALLOWED_TYPES,
        "max_file_sizes_mb": {
            file_type: size // (1024 * 1024)
            for file_type, size in file_storage.MAX_FILE_SIZES.items()
        },
        "max_files_per_upload": 10
    }
