from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_active_vendor
from app.schemas.chat import Chat<PERSON><PERSON>, ChatUpdate, ChatResponse, ChatWithMessages, ChatListResponse
from app.services.chat import chat_service
from app.models.vendor import Vendor
from app.models.chat import SenderType

router = APIRouter()


@router.post("/", response_model=ChatResponse, status_code=status.HTTP_201_CREATED)
async def create_chat(
    chat_data: ChatCreate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Create a new chat between vendor and customer.
    """
    return chat_service.create_chat(db, chat_data, str(current_vendor.id))


@router.get("/", response_model=ChatListResponse)
async def get_vendor_chats(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    status: Optional[str] = Query(None, description="Filter by chat status"),
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get all chats for the current vendor.
    """
    return chat_service.get_vendor_chats(
        db, str(current_vendor.id), skip=skip, limit=limit, status=status
    )


@router.get("/{chat_id}", response_model=ChatWithMessages)
async def get_chat(
    chat_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Get chat with messages (only participants can access).
    """
    chat = chat_service.get_chat(db, chat_id)
    
    if not chat:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )
    
    # Check if vendor is participant
    if str(chat.vendor_id) != str(current_vendor.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return chat


@router.put("/{chat_id}", response_model=ChatResponse)
async def update_chat(
    chat_id: str,
    chat_data: ChatUpdate,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Update chat status.
    """
    updated_chat = chat_service.update_chat(
        db, chat_id, chat_data, str(current_vendor.id), SenderType.VENDOR
    )
    
    if not updated_chat:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )
    
    return updated_chat


@router.delete("/{chat_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_chat(
    chat_id: str,
    current_vendor: Vendor = Depends(get_current_active_vendor),
    db: Session = Depends(get_db)
):
    """
    Delete chat (soft delete).
    """
    success = chat_service.delete_chat(
        db, chat_id, str(current_vendor.id), SenderType.VENDOR
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat not found"
        )
