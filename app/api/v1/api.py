from fastapi import APIRouter
from app.api.v1.endpoints import vendors, categories, products, auth, customers, chats, messages, uploads

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(vendors.router, prefix="/vendors", tags=["vendors"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(products.router, prefix="/products", tags=["products"])
api_router.include_router(customers.router, prefix="/customers", tags=["customers"])
api_router.include_router(chats.router, prefix="/chats", tags=["chats"])
api_router.include_router(messages.router, prefix="", tags=["messages"])  # No prefix for messages
api_router.include_router(uploads.router, prefix="/uploads", tags=["file uploads"])
