from fastapi import APIRouter
from app.api.v1.endpoints import vendors, categories, products, auth

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(vendors.router, prefix="/vendors", tags=["vendors"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(products.router, prefix="/products", tags=["products"])
