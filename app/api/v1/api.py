from fastapi import APIRouter
from app.api.v1.endpoints import (
    vendors, categories, products, auth, customers, chats, messages, uploads,
    plans, subscriptions, in_app_purchases, purchases, payments
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(vendors.router, prefix="/vendors", tags=["vendors"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(products.router, prefix="/products", tags=["products"])
api_router.include_router(customers.router, prefix="/customers", tags=["customers"])
api_router.include_router(chats.router, prefix="/chats", tags=["chats"])
api_router.include_router(messages.router, prefix="", tags=["messages"])  # No prefix for messages
api_router.include_router(uploads.router, prefix="/uploads", tags=["file uploads"])

# Monetization endpoints
api_router.include_router(plans.router, prefix="/plans", tags=["subscription plans"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["subscriptions"])
api_router.include_router(in_app_purchases.router, prefix="/in-app-purchases", tags=["in-app purchases"])
api_router.include_router(purchases.router, prefix="/purchases", tags=["purchases"])
api_router.include_router(payments.router, prefix="/payments", tags=["payments"])
