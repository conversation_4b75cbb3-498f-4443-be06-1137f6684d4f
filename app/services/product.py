from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from math import ceil
from app.models.product import Product
from app.schemas.product import (
    ProductCreate, ProductUpdate, ProductResponse, 
    ProductListResponse, ProductsListResponse
)
from app.repositories.product import product_repository
from app.repositories.category import category_repository
from app.repositories.vendor import vendor_repository


class ProductService:
    def __init__(self):
        self.repository = product_repository
        self.category_repository = category_repository
        self.vendor_repository = vendor_repository

    def create_product(
        self,
        db: Session,
        product_data: ProductCreate,
        vendor_id: str
    ) -> ProductResponse:
        """Create a new product."""
        # Verify vendor exists
        vendor = self.vendor_repository.get(db, vendor_id)
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        # Verify category exists
        category = self.category_repository.get(db, product_data.category_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Category not found"
            )
        
        # Prepare product data
        product_dict = product_data.model_dump(exclude={"images", "variants"})
        product_dict["vendor_id"] = vendor_id
        
        # Extract images and variants
        images = product_data.images or []
        variants = product_data.variants or []
        
        # Create product with relations
        db_product = self.repository.create_with_relations(
            db,
            product_data=product_dict,
            images=[img.model_dump() for img in images],
            variants=[var.model_dump() for var in variants]
        )
        
        # Get product with relations for response
        product_with_relations = self.repository.get_with_relations(db, db_product.id)
        return ProductResponse.model_validate(product_with_relations)

    def get_product(self, db: Session, product_id: str) -> Optional[ProductResponse]:
        """Get product by ID with relations."""
        product = self.repository.get_with_relations(db, product_id)
        if not product:
            return None
        
        return ProductResponse.model_validate(product)

    def get_products(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        vendor_id: Optional[str] = None,
        category_id: Optional[str] = None,
        status: Optional[str] = None,
        is_featured: Optional[bool] = None,
        search: Optional[str] = None,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> ProductsListResponse:
        """Get products with filtering, pagination, and search."""
        
        # Build filters
        filters = {}
        if vendor_id:
            filters["vendor_id"] = vendor_id
        if category_id:
            filters["category_id"] = category_id
        if status:
            filters["status"] = status
        if is_featured is not None:
            filters["is_featured"] = is_featured
        
        # Get products based on search or filters
        if search:
            products = self.repository.search_products(
                db, query=search, skip=skip, limit=limit, filters=filters
            )
            total = len(self.repository.search_products(db, query=search, skip=0, limit=10000, filters=filters))
        else:
            products = self.repository.get_multi(
                db, skip=skip, limit=limit, filters=filters, 
                order_by=order_by, order_desc=order_desc
            )
            total = self.repository.count(db, filters=filters)
        
        # Convert to list response format
        product_list = []
        for product in products:
            # Get primary image
            primary_image = None
            if product.images:
                for img in product.images:
                    if img.is_primary:
                        primary_image = img.image_url
                        break
                if not primary_image and product.images:
                    primary_image = product.images[0].image_url
            
            product_item = ProductListResponse(
                id=product.id,
                name=product.name,
                description=product.description,
                price=product.price,
                status=product.status,
                is_featured=product.is_featured,
                vendor_id=product.vendor_id,
                category_id=product.category_id,
                created_at=product.created_at,
                primary_image=primary_image
            )
            product_list.append(product_item)
        
        # Calculate pagination info
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return ProductsListResponse(
            products=product_list,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def update_product(
        self,
        db: Session,
        product_id: str,
        product_data: ProductUpdate,
        vendor_id: str
    ) -> Optional[ProductResponse]:
        """Update product."""
        product = self.repository.get(db, product_id)
        if not product:
            return None
        
        # Check if product belongs to vendor
        if str(product.vendor_id) != vendor_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this product"
            )
        
        # Verify category if being updated
        if product_data.category_id:
            category = self.category_repository.get(db, product_data.category_id)
            if not category:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Category not found"
                )
        
        # Update product
        update_dict = product_data.model_dump(exclude_unset=True)
        updated_product = self.repository.update(db, db_obj=product, obj_in=update_dict)
        
        # Get updated product with relations
        product_with_relations = self.repository.get_with_relations(db, updated_product.id)
        return ProductResponse.model_validate(product_with_relations)

    def delete_product(self, db: Session, product_id: str, vendor_id: str) -> bool:
        """Soft delete product."""
        product = self.repository.get(db, product_id)
        if not product:
            return False
        
        # Check if product belongs to vendor
        if str(product.vendor_id) != vendor_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to delete this product"
            )
        
        self.repository.delete(db, id=product_id)
        return True

    def get_featured_products(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100
    ) -> List[ProductListResponse]:
        """Get featured products."""
        products = self.repository.get_featured_products(db, skip=skip, limit=limit)
        
        product_list = []
        for product in products:
            # Get primary image
            primary_image = None
            if product.images:
                for img in product.images:
                    if img.is_primary:
                        primary_image = img.image_url
                        break
                if not primary_image and product.images:
                    primary_image = product.images[0].image_url
            
            product_item = ProductListResponse(
                id=product.id,
                name=product.name,
                description=product.description,
                price=product.price,
                status=product.status,
                is_featured=product.is_featured,
                vendor_id=product.vendor_id,
                category_id=product.category_id,
                created_at=product.created_at,
                primary_image=primary_image
            )
            product_list.append(product_item)
        
        return product_list


# Create service instance
product_service = ProductService()
