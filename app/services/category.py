from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Ex<PERSON>, status
from app.models.category import Category
from app.schemas.category import CategoryCreate, CategoryUpdate, CategoryResponse
from app.repositories.category import category_repository


class CategoryService:
    def __init__(self):
        self.repository = category_repository

    def create_category(self, db: Session, category_data: CategoryCreate) -> CategoryResponse:
        """Create a new category."""
        # Check if name already exists
        existing_category = self.repository.get_by_name(db, name=category_data.name)
        if existing_category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category name already exists"
            )
        
        # Create category
        category_dict = category_data.model_dump()
        db_category = self.repository.create(db, obj_in=category_dict)
        
        return CategoryResponse.model_validate(db_category)

    def get_category(self, db: Session, category_id: str) -> Optional[CategoryResponse]:
        """Get category by ID."""
        category = self.repository.get(db, category_id)
        if not category:
            return None
        
        return CategoryResponse.model_validate(category)

    def get_categories(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> List[CategoryResponse]:
        """Get all categories."""
        if active_only:
            categories = self.repository.get_active_categories(db, skip=skip, limit=limit)
        else:
            categories = self.repository.get_multi(db, skip=skip, limit=limit, order_by="name")
        
        return [CategoryResponse.model_validate(category) for category in categories]

    def search_categories(
        self,
        db: Session,
        name: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[CategoryResponse]:
        """Search categories by name."""
        categories = self.repository.search_by_name(db, name=name, skip=skip, limit=limit)
        return [CategoryResponse.model_validate(category) for category in categories]

    def update_category(
        self,
        db: Session,
        category_id: str,
        category_data: CategoryUpdate
    ) -> Optional[CategoryResponse]:
        """Update category."""
        category = self.repository.get(db, category_id)
        if not category:
            return None
        
        # Check if name is being changed and if it's already taken
        if category_data.name and category_data.name != category.name:
            existing_category = self.repository.get_by_name(db, name=category_data.name)
            if existing_category:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Category name already exists"
                )
        
        # Update category
        update_dict = category_data.model_dump(exclude_unset=True)
        updated_category = self.repository.update(db, db_obj=category, obj_in=update_dict)
        
        return CategoryResponse.model_validate(updated_category)

    def delete_category(self, db: Session, category_id: str) -> bool:
        """Soft delete category."""
        category = self.repository.get(db, category_id)
        if not category:
            return False
        
        self.repository.delete(db, id=category_id)
        return True

    def get_category_count(self, db: Session, active_only: bool = True) -> int:
        """Get total count of categories."""
        filters = {"is_active": True} if active_only else None
        return self.repository.count(db, filters=filters)


# Create service instance
category_service = CategoryService()
