from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Ex<PERSON>, status
from math import ceil
from app.models.chat import Message, SenderType, MessageStatus
from app.schemas.chat import MessageCreate, MessageUpdate, MessageResponse, MessageListResponse
from app.repositories.chat import chat_repository, message_repository


class MessageService:
    def __init__(self):
        self.chat_repository = chat_repository
        self.message_repository = message_repository

    def send_message(
        self,
        db: Session,
        chat_id: str,
        message_data: MessageCreate,
        sender_id: str,
        sender_type: SenderType
    ) -> MessageResponse:
        """Send a message in a chat."""
        # Verify chat exists and user is participant
        chat = self.chat_repository.get(db, chat_id)
        if not chat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chat not found"
            )
        
        # Check if user is participant
        if sender_type == SenderType.VENDOR and str(chat.vendor_id) != sender_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        elif sender_type == SenderType.CUSTOMER and str(chat.customer_id) != sender_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Create message
        message_dict = message_data.model_dump()
        message_dict.update({
            "chat_id": chat_id,
            "sender_id": sender_id,
            "sender_type": sender_type
        })
        
        # Convert attachments to JSON format
        if message_dict.get("attachments"):
            message_dict["attachments"] = [att.model_dump() for att in message_data.attachments]
        
        db_message = self.message_repository.create(db, obj_in=message_dict)
        
        # Update chat's last message time
        self.chat_repository.update_last_message_time(db, chat_id)
        
        return MessageResponse.model_validate(db_message)

    def get_message(self, db: Session, message_id: str) -> Optional[MessageResponse]:
        """Get message by ID."""
        message = self.message_repository.get(db, message_id)
        if not message:
            return None
        
        return MessageResponse.model_validate(message)

    def get_chat_messages(
        self,
        db: Session,
        chat_id: str,
        user_id: str,
        user_type: SenderType,
        skip: int = 0,
        limit: int = 100,
        order_desc: bool = False
    ) -> MessageListResponse:
        """Get messages for a chat (only participants can access)."""
        # Verify chat exists and user is participant
        chat = self.chat_repository.get(db, chat_id)
        if not chat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chat not found"
            )
        
        # Check if user is participant
        if user_type == SenderType.VENDOR and str(chat.vendor_id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        elif user_type == SenderType.CUSTOMER and str(chat.customer_id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get messages
        messages = self.message_repository.get_chat_messages(
            db, chat_id=chat_id, skip=skip, limit=limit, order_desc=order_desc
        )
        
        total = self.message_repository.count(db, filters={"chat_id": chat_id})
        
        message_responses = [MessageResponse.model_validate(msg) for msg in messages]
        
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return MessageListResponse(
            messages=message_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def update_message(
        self,
        db: Session,
        message_id: str,
        message_data: MessageUpdate,
        user_id: str,
        user_type: SenderType
    ) -> Optional[MessageResponse]:
        """Update message (only sender can update)."""
        message = self.message_repository.get(db, message_id)
        if not message:
            return None
        
        # Check if user is the sender
        if str(message.sender_id) != user_id or message.sender_type != user_type:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only the sender can update this message"
            )
        
        # Update message
        update_dict = message_data.model_dump(exclude_unset=True)
        updated_message = self.message_repository.update(db, db_obj=message, obj_in=update_dict)
        
        return MessageResponse.model_validate(updated_message)

    def delete_message(
        self,
        db: Session,
        message_id: str,
        user_id: str,
        user_type: SenderType
    ) -> bool:
        """Soft delete message (only sender can delete)."""
        message = self.message_repository.get(db, message_id)
        if not message:
            return False
        
        # Check if user is the sender
        if str(message.sender_id) != user_id or message.sender_type != user_type:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only the sender can delete this message"
            )
        
        self.message_repository.delete(db, id=message_id)
        return True

    def mark_messages_as_read(
        self,
        db: Session,
        chat_id: str,
        reader_id: str,
        reader_type: SenderType
    ) -> int:
        """Mark all unread messages in a chat as read."""
        # Verify chat exists and user is participant
        chat = self.chat_repository.get(db, chat_id)
        if not chat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chat not found"
            )
        
        # Check if user is participant
        if reader_type == SenderType.VENDOR and str(chat.vendor_id) != reader_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        elif reader_type == SenderType.CUSTOMER and str(chat.customer_id) != reader_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return self.message_repository.mark_messages_as_read(
            db, chat_id=chat_id, reader_type=reader_type, reader_id=reader_id
        )

    def get_unread_count(
        self,
        db: Session,
        chat_id: str,
        user_id: str,
        user_type: SenderType
    ) -> int:
        """Get count of unread messages for a user in a chat."""
        return self.message_repository.get_unread_count(
            db, chat_id=chat_id, for_sender_type=user_type, sender_id=user_id
        )


# Create service instance
message_service = MessageService()
