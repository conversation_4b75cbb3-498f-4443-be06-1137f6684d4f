from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from app.models.vendor import <PERSON><PERSON><PERSON>, VendorRole
from app.schemas.vendor import VendorCreate, VendorUpdate, VendorResponse
from app.repositories.vendor import vendor_repository
from app.core.security import create_access_token
from app.core.permissions import can_modify_vendor_role


class VendorService:
    def __init__(self):
        self.repository = vendor_repository

    def create_vendor(self, db: Session, vendor_data: VendorCreate) -> VendorResponse:
        """Create a new vendor."""
        # Check if phone already exists
        existing_vendor = self.repository.get_by_phone(db, phone=vendor_data.phone)
        if existing_vendor:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number already registered"
            )
        
        # Create vendor
        vendor_dict = vendor_data.model_dump()
        db_vendor = self.repository.create_vendor(db, vendor_data=vendor_dict)
        
        return VendorResponse.model_validate(db_vendor)

    def get_vendor(self, db: Session, vendor_id: str) -> Optional[VendorResponse]:
        """Get vendor by ID."""
        vendor = self.repository.get(db, vendor_id)
        if not vendor:
            return None
        
        return VendorResponse.model_validate(vendor)

    def get_vendor_by_phone(self, db: Session, phone: str) -> Optional[VendorResponse]:
        """Get vendor by phone."""
        vendor = self.repository.get_by_phone(db, phone=phone)
        if not vendor:
            return None
        
        return VendorResponse.model_validate(vendor)

    def update_vendor(
        self,
        db: Session,
        vendor_id: str,
        vendor_data: VendorUpdate,
        current_vendor: Optional[Vendor] = None
    ) -> Optional[VendorResponse]:
        """Update vendor."""
        vendor = self.repository.get(db, vendor_id)
        if not vendor:
            return None

        # Check if phone is being changed and if it's already taken
        if vendor_data.phone and vendor_data.phone != vendor.phone:
            existing_vendor = self.repository.get_by_phone(db, phone=vendor_data.phone)
            if existing_vendor:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Phone number already registered"
                )

        # Handle role changes (only admins can change roles)
        if vendor_data.role is not None and current_vendor:
            if not can_modify_vendor_role(current_vendor, vendor):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Only admins can modify vendor roles, and admins cannot demote themselves"
                )

        # Update vendor
        update_dict = vendor_data.model_dump(exclude_unset=True)
        updated_vendor = self.repository.update(db, db_obj=vendor, obj_in=update_dict)

        return VendorResponse.model_validate(updated_vendor)

    def delete_vendor(self, db: Session, vendor_id: str) -> bool:
        """Soft delete vendor."""
        vendor = self.repository.get(db, vendor_id)
        if not vendor:
            return False
        
        self.repository.delete(db, id=vendor_id)
        return True

    def authenticate_vendor(self, db: Session, phone: str, password: str) -> Optional[dict]:
        """Authenticate vendor and return token."""
        vendor = self.repository.authenticate(db, phone=phone, password=password)
        if not vendor:
            return None
        
        if not self.repository.is_active(vendor):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive vendor account"
            )
        
        # Create access token
        access_token = create_access_token(data={"sub": str(vendor.id)})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "vendor": VendorResponse.model_validate(vendor)
        }

    def create_admin_vendor(self, db: Session, vendor_data: VendorCreate) -> VendorResponse:
        """Create a new admin vendor (only for initial setup)."""
        # Check if phone already exists
        existing_vendor = self.repository.get_by_phone(db, phone=vendor_data.phone)
        if existing_vendor:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number already registered"
            )

        # Create admin vendor
        vendor_dict = vendor_data.model_dump()
        vendor_dict["role"] = VendorRole.ADMIN  # Force admin role
        db_vendor = self.repository.create_vendor(db, vendor_data=vendor_dict)

        return VendorResponse.model_validate(db_vendor)


# Create service instance
vendor_service = VendorService()
