from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from math import ceil
from app.models.customer import Customer
from app.schemas.customer import CustomerCreate, CustomerUpdate, CustomerResponse, CustomerListResponse
from app.repositories.customer import customer_repository


class CustomerService:
    def __init__(self):
        self.repository = customer_repository

    def create_customer(self, db: Session, customer_data: CustomerCreate) -> CustomerResponse:
        """Create a new customer."""
        # Check if phone already exists
        existing_customer = self.repository.get_by_phone(db, phone=customer_data.phone)
        if existing_customer:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number already registered"
            )
        
        # Check if email already exists (if provided)
        if customer_data.email:
            existing_customer_email = self.repository.get_by_email(db, email=customer_data.email)
            if existing_customer_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
        
        # Create customer
        customer_dict = customer_data.model_dump()
        db_customer = self.repository.create_customer(db, customer_data=customer_dict)
        
        return CustomerResponse.model_validate(db_customer)

    def get_customer(self, db: Session, customer_id: str) -> Optional[CustomerResponse]:
        """Get customer by ID."""
        customer = self.repository.get(db, customer_id)
        if not customer:
            return None
        
        return CustomerResponse.model_validate(customer)

    def get_customer_by_phone(self, db: Session, phone: str) -> Optional[CustomerResponse]:
        """Get customer by phone number."""
        customer = self.repository.get_by_phone(db, phone=phone)
        if not customer:
            return None
        
        return CustomerResponse.model_validate(customer)

    def get_customers(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        active_only: bool = True
    ) -> CustomerListResponse:
        """Get customers with filtering and pagination."""
        
        if search:
            customers = self.repository.search_customers(
                db, query=search, skip=skip, limit=limit
            )
            total = len(self.repository.search_customers(db, query=search, skip=0, limit=10000))
        elif active_only:
            customers = self.repository.get_active_customers(db, skip=skip, limit=limit)
            total = self.repository.count(db, filters={"status": "active"})
        else:
            customers = self.repository.get_multi(db, skip=skip, limit=limit, order_by="created_at", order_desc=True)
            total = self.repository.count(db)
        
        # Convert to response format
        customer_responses = [CustomerResponse.model_validate(customer) for customer in customers]
        
        # Calculate pagination info
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return CustomerListResponse(
            customers=customer_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def update_customer(
        self,
        db: Session,
        customer_id: str,
        customer_data: CustomerUpdate
    ) -> Optional[CustomerResponse]:
        """Update customer."""
        customer = self.repository.get(db, customer_id)
        if not customer:
            return None
        
        # Check if phone is being changed and if it's already taken
        if customer_data.phone and customer_data.phone != customer.phone:
            existing_customer = self.repository.get_by_phone(db, phone=customer_data.phone)
            if existing_customer:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Phone number already registered"
                )
        
        # Check if email is being changed and if it's already taken
        if customer_data.email and customer_data.email != customer.email:
            existing_customer_email = self.repository.get_by_email(db, email=customer_data.email)
            if existing_customer_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
        
        # Update customer
        update_dict = customer_data.model_dump(exclude_unset=True)
        updated_customer = self.repository.update(db, db_obj=customer, obj_in=update_dict)
        
        return CustomerResponse.model_validate(updated_customer)

    def delete_customer(self, db: Session, customer_id: str) -> bool:
        """Soft delete customer."""
        customer = self.repository.get(db, customer_id)
        if not customer:
            return False
        
        self.repository.delete(db, id=customer_id)
        return True

    def get_or_create_customer(self, db: Session, phone: str, name: str = None) -> CustomerResponse:
        """Get existing customer by phone or create new one."""
        customer = self.repository.get_by_phone(db, phone=phone)
        
        if customer:
            return CustomerResponse.model_validate(customer)
        
        # Create new customer
        customer_data = CustomerCreate(
            name=name or f"Customer {phone}",
            phone=phone
        )
        
        return self.create_customer(db, customer_data)


# Create service instance
customer_service = CustomerService()
