from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from math import ceil
from app.models.monetization import (
    Plan, Subscription, InAppPurchase, Purchase, Payment,
    SubscriptionStatus, PaymentStatus, PlanType, PlanDuration
)
from app.schemas.monetization import (
    PlanCreate, PlanUpdate, PlanResponse, PlanListResponse,
    SubscriptionCreate, SubscriptionUpdate, SubscriptionResponse, SubscriptionListResponse,
    InAppPurchaseCreate, InAppPurchaseUpdate, InAppPurchaseResponse, InAppPurchaseListResponse,
    PurchaseCreate, PurchaseResponse, PurchaseListResponse,
    PaymentCreate, PaymentUpdate, PaymentResponse, PaymentListResponse,
    SubscriptionStatusResponse
)
from app.repositories.monetization import (
    plan_repository, subscription_repository, in_app_purchase_repository,
    purchase_repository, payment_repository
)


class PlanService:
    def __init__(self):
        self.repository = plan_repository

    def create_plan(self, db: Session, plan_data: PlanCreate) -> PlanResponse:
        """Create a new plan."""
        # Check if plan name already exists
        existing_plan = db.query(Plan).filter(Plan.name == plan_data.name).first()
        if existing_plan:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Plan name already exists"
            )

        plan_dict = plan_data.model_dump()
        db_plan = self.repository.create(db, obj_in=plan_dict)
        return PlanResponse.model_validate(db_plan)

    def get_plan(self, db: Session, plan_id: str) -> Optional[PlanResponse]:
        """Get plan by ID."""
        plan = self.repository.get(db, plan_id)
        if not plan:
            return None
        return PlanResponse.model_validate(plan)

    def get_plans(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> PlanListResponse:
        """Get all plans with pagination."""
        if active_only:
            plans = self.repository.get_active_plans(db, skip=skip, limit=limit)
            total = self.repository.count(db, filters={"is_active": True})
        else:
            plans = self.repository.get_multi(db, skip=skip, limit=limit, order_by="price")
            total = self.repository.count(db)

        plan_responses = [PlanResponse.model_validate(plan) for plan in plans]
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1

        return PlanListResponse(
            plans=plan_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def update_plan(
        self,
        db: Session,
        plan_id: str,
        plan_data: PlanUpdate
    ) -> Optional[PlanResponse]:
        """Update plan."""
        plan = self.repository.get(db, plan_id)
        if not plan:
            return None

        # Check if name is being changed and if it's already taken
        if plan_data.name and plan_data.name != plan.name:
            existing_plan = db.query(Plan).filter(Plan.name == plan_data.name).first()
            if existing_plan:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Plan name already exists"
                )

        update_dict = plan_data.model_dump(exclude_unset=True)
        updated_plan = self.repository.update(db, db_obj=plan, obj_in=update_dict)
        return PlanResponse.model_validate(updated_plan)

    def delete_plan(self, db: Session, plan_id: str) -> bool:
        """Soft delete plan."""
        plan = self.repository.get(db, plan_id)
        if not plan:
            return False

        # Check if plan has active subscriptions
        active_subscriptions = subscription_repository.count(
            db, filters={"plan_id": plan_id, "status": SubscriptionStatus.ACTIVE}
        )
        if active_subscriptions > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete plan with active subscriptions"
            )

        self.repository.delete(db, id=plan_id)
        return True

    def get_free_plan(self, db: Session) -> Optional[PlanResponse]:
        """Get the free plan."""
        plan = self.repository.get_free_plan(db)
        if not plan:
            return None
        return PlanResponse.model_validate(plan)


class SubscriptionService:
    def __init__(self):
        self.repository = subscription_repository
        self.plan_repository = plan_repository

    def create_subscription(
        self,
        db: Session,
        subscription_data: SubscriptionCreate,
        vendor_id: str
    ) -> SubscriptionResponse:
        """Create a new subscription for a vendor."""
        # Check if vendor already has an active subscription
        existing_subscription = self.repository.get_vendor_active_subscription(db, vendor_id=vendor_id)
        if existing_subscription:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Vendor already has an active subscription"
            )

        # Get plan details
        plan = self.plan_repository.get(db, subscription_data.plan_id)
        if not plan or not plan.is_active:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plan not found or inactive"
            )

        # Calculate end date based on plan duration
        start_date = datetime.utcnow()
        if plan.duration == PlanDuration.MONTHLY:
            end_date = start_date + timedelta(days=30)
        elif plan.duration == PlanDuration.QUARTERLY:
            end_date = start_date + timedelta(days=90)
        elif plan.duration == PlanDuration.YEARLY:
            end_date = start_date + timedelta(days=365)
        else:  # LIFETIME
            end_date = start_date + timedelta(days=365 * 100)  # 100 years

        subscription_dict = subscription_data.model_dump()
        subscription_dict.update({
            "vendor_id": vendor_id,
            "start_date": start_date,
            "end_date": end_date
        })

        db_subscription = self.repository.create(db, obj_in=subscription_dict)
        return self._build_subscription_response(db, db_subscription)

    def get_subscription(self, db: Session, subscription_id: str) -> Optional[SubscriptionResponse]:
        """Get subscription by ID."""
        subscription = self.repository.get_with_plan(db, subscription_id)
        if not subscription:
            return None
        return self._build_subscription_response(db, subscription)

    def get_vendor_active_subscription(self, db: Session, vendor_id: str) -> Optional[SubscriptionResponse]:
        """Get vendor's active subscription."""
        subscription = self.repository.get_vendor_active_subscription(db, vendor_id=vendor_id)
        if not subscription:
            return None
        return self._build_subscription_response(db, subscription)

    def get_vendor_subscriptions(
        self,
        db: Session,
        vendor_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> SubscriptionListResponse:
        """Get all subscriptions for a vendor."""
        subscriptions = self.repository.get_vendor_subscriptions(
            db, vendor_id=vendor_id, skip=skip, limit=limit
        )
        total = self.repository.count(db, filters={"vendor_id": vendor_id})

        subscription_responses = [
            self._build_subscription_response(db, sub) for sub in subscriptions
        ]
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1

        return SubscriptionListResponse(
            subscriptions=subscription_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def update_subscription(
        self,
        db: Session,
        subscription_id: str,
        subscription_data: SubscriptionUpdate
    ) -> Optional[SubscriptionResponse]:
        """Update subscription."""
        subscription = self.repository.get(db, subscription_id)
        if not subscription:
            return None

        update_dict = subscription_data.model_dump(exclude_unset=True)
        updated_subscription = self.repository.update(db, db_obj=subscription, obj_in=update_dict)
        return self._build_subscription_response(db, updated_subscription)

    def cancel_subscription(self, db: Session, subscription_id: str) -> Optional[SubscriptionResponse]:
        """Cancel subscription."""
        subscription = self.repository.get(db, subscription_id)
        if not subscription:
            return None

        subscription.status = SubscriptionStatus.CANCELLED
        subscription.auto_renew = False
        db.add(subscription)
        db.commit()
        db.refresh(subscription)

        return self._build_subscription_response(db, subscription)

    def check_subscription_status(self, db: Session, vendor_id: str) -> SubscriptionStatusResponse:
        """Check vendor's subscription status and limits."""
        subscription = self.repository.get_vendor_active_subscription(db, vendor_id=vendor_id)
        
        if not subscription:
            return SubscriptionStatusResponse(
                has_active_subscription=False,
                current_subscription=None,
                plan_limits=None,
                usage_stats=None
            )

        plan_limits = {
            "max_products": subscription.plan.max_products,
            "max_images_per_product": subscription.plan.max_images_per_product,
            "max_file_uploads_mb": subscription.plan.max_file_uploads_mb,
            "chat_support": subscription.plan.chat_support,
            "analytics_access": subscription.plan.analytics_access,
            "priority_listing": subscription.plan.priority_listing
        }

        usage_stats = {
            "current_products_count": subscription.current_products_count,
            "current_file_usage_mb": float(subscription.current_file_usage_mb),
            "products_remaining": max(0, subscription.plan.max_products - subscription.current_products_count),
            "file_storage_remaining_mb": max(0, subscription.plan.max_file_uploads_mb - float(subscription.current_file_usage_mb))
        }

        return SubscriptionStatusResponse(
            has_active_subscription=True,
            current_subscription=self._build_subscription_response(db, subscription),
            plan_limits=plan_limits,
            usage_stats=usage_stats
        )

    def process_expired_subscriptions(self, db: Session) -> int:
        """Process expired subscriptions (called by scheduler)."""
        expired_subscriptions = self.repository.get_expired_subscriptions(db)
        count = 0

        for subscription in expired_subscriptions:
            subscription.status = SubscriptionStatus.EXPIRED
            db.add(subscription)
            count += 1

        db.commit()
        return count

    def _build_subscription_response(self, db: Session, subscription: Subscription) -> SubscriptionResponse:
        """Build subscription response with plan data."""
        response_data = SubscriptionResponse.model_validate(subscription)
        if subscription.plan:
            response_data.plan = PlanResponse.model_validate(subscription.plan)
        return response_data


class InAppPurchaseService:
    def __init__(self):
        self.repository = in_app_purchase_repository

    def create_in_app_purchase(self, db: Session, purchase_data: InAppPurchaseCreate) -> InAppPurchaseResponse:
        """Create a new in-app purchase."""
        purchase_dict = purchase_data.model_dump()
        db_purchase = self.repository.create(db, obj_in=purchase_dict)
        return InAppPurchaseResponse.model_validate(db_purchase)

    def get_in_app_purchase(self, db: Session, purchase_id: str) -> Optional[InAppPurchaseResponse]:
        """Get in-app purchase by ID."""
        purchase = self.repository.get(db, purchase_id)
        if not purchase:
            return None
        return InAppPurchaseResponse.model_validate(purchase)

    def get_in_app_purchases(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> InAppPurchaseListResponse:
        """Get all in-app purchases with pagination."""
        if active_only:
            purchases = self.repository.get_active_purchases(db, skip=skip, limit=limit)
            total = self.repository.count(db, filters={"is_active": True})
        else:
            purchases = self.repository.get_multi(db, skip=skip, limit=limit, order_by="price")
            total = self.repository.count(db)

        purchase_responses = [InAppPurchaseResponse.model_validate(p) for p in purchases]
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1

        return InAppPurchaseListResponse(
            in_app_purchases=purchase_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def update_in_app_purchase(
        self,
        db: Session,
        purchase_id: str,
        purchase_data: InAppPurchaseUpdate
    ) -> Optional[InAppPurchaseResponse]:
        """Update in-app purchase."""
        purchase = self.repository.get(db, purchase_id)
        if not purchase:
            return None

        update_dict = purchase_data.model_dump(exclude_unset=True)
        updated_purchase = self.repository.update(db, db_obj=purchase, obj_in=update_dict)
        return InAppPurchaseResponse.model_validate(updated_purchase)

    def delete_in_app_purchase(self, db: Session, purchase_id: str) -> bool:
        """Soft delete in-app purchase."""
        purchase = self.repository.get(db, purchase_id)
        if not purchase:
            return False

        self.repository.delete(db, id=purchase_id)
        return True


class PurchaseService:
    def __init__(self):
        self.repository = purchase_repository
        self.subscription_repository = subscription_repository
        self.in_app_purchase_repository = in_app_purchase_repository

    def create_purchase(
        self,
        db: Session,
        purchase_data: PurchaseCreate,
        vendor_id: str
    ) -> PurchaseResponse:
        """Create a new purchase."""
        # Validate the purchase reference based on type
        if purchase_data.purchase_type.value == "subscription":
            if not purchase_data.subscription_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="subscription_id is required for subscription purchases"
                )
            # Verify subscription exists
            subscription = self.subscription_repository.get(db, purchase_data.subscription_id)
            if not subscription:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Subscription not found"
                )
        else:  # in_app_purchase
            if not purchase_data.in_app_purchase_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="in_app_purchase_id is required for in-app purchases"
                )
            # Verify in-app purchase exists
            in_app_purchase = self.in_app_purchase_repository.get(db, purchase_data.in_app_purchase_id)
            if not in_app_purchase:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="In-app purchase not found"
                )

        purchase_dict = purchase_data.model_dump()
        purchase_dict["vendor_id"] = vendor_id

        db_purchase = self.repository.create(db, obj_in=purchase_dict)
        return self._build_purchase_response(db, db_purchase)

    def get_purchase(self, db: Session, purchase_id: str) -> Optional[PurchaseResponse]:
        """Get purchase by ID."""
        purchase = self.repository.get_with_relations(db, purchase_id)
        if not purchase:
            return None
        return self._build_purchase_response(db, purchase)

    def get_vendor_purchases(
        self,
        db: Session,
        vendor_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> PurchaseListResponse:
        """Get all purchases for a vendor."""
        purchases = self.repository.get_vendor_purchases(
            db, vendor_id=vendor_id, skip=skip, limit=limit
        )
        total = self.repository.count(db, filters={"vendor_id": vendor_id})

        purchase_responses = [
            self._build_purchase_response(db, purchase) for purchase in purchases
        ]
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1

        return PurchaseListResponse(
            purchases=purchase_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def _build_purchase_response(self, db: Session, purchase: Purchase) -> PurchaseResponse:
        """Build purchase response with related data."""
        response_data = PurchaseResponse.model_validate(purchase)

        if purchase.subscription and purchase.subscription.plan:
            response_data.subscription = SubscriptionResponse.model_validate(purchase.subscription)
            response_data.subscription.plan = PlanResponse.model_validate(purchase.subscription.plan)

        if purchase.in_app_purchase:
            response_data.in_app_purchase = InAppPurchaseResponse.model_validate(purchase.in_app_purchase)

        return response_data


class PaymentService:
    def __init__(self):
        self.repository = payment_repository
        self.purchase_repository = purchase_repository

    def create_payment(
        self,
        db: Session,
        payment_data: PaymentCreate
    ) -> PaymentResponse:
        """Create a new payment."""
        # Verify purchase exists
        purchase = self.purchase_repository.get(db, payment_data.purchase_id)
        if not purchase:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Purchase not found"
            )

        payment_dict = payment_data.model_dump()
        db_payment = self.repository.create(db, obj_in=payment_dict)
        return PaymentResponse.model_validate(db_payment)

    def get_payment(self, db: Session, payment_id: str) -> Optional[PaymentResponse]:
        """Get payment by ID."""
        payment = self.repository.get(db, payment_id)
        if not payment:
            return None
        return PaymentResponse.model_validate(payment)

    def update_payment_status(
        self,
        db: Session,
        payment_id: str,
        payment_data: PaymentUpdate,
        processed_by: str = None
    ) -> Optional[PaymentResponse]:
        """Update payment status."""
        payment = self.repository.update_payment_status(
            db,
            payment_id=payment_id,
            status=payment_data.status,
            external_reference=payment_data.external_reference,
            gateway_response=payment_data.gateway_response,
            admin_notes=payment_data.admin_notes,
            processed_by=processed_by
        )

        if not payment:
            return None

        return PaymentResponse.model_validate(payment)

    def get_purchase_payments(
        self,
        db: Session,
        purchase_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> PaymentListResponse:
        """Get all payments for a purchase."""
        payments = self.repository.get_purchase_payments(
            db, purchase_id=purchase_id, skip=skip, limit=limit
        )
        total = self.repository.count(db, filters={"purchase_id": purchase_id})

        payment_responses = [PaymentResponse.model_validate(payment) for payment in payments]
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1

        return PaymentListResponse(
            payments=payment_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )


# Create service instances
plan_service = PlanService()
subscription_service = SubscriptionService()
in_app_purchase_service = InAppPurchaseService()
purchase_service = PurchaseService()
payment_service = PaymentService()
