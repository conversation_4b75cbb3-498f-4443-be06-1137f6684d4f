from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from math import ceil
from app.models.chat import Chat, Message, SenderType, MessageStatus
from app.schemas.chat import (
    Chat<PERSON>reate, ChatUpdate, ChatResponse, ChatWithMessages, ChatListResponse,
    MessageCreate, Message<PERSON>pdate, MessageResponse, MessageListResponse
)
from app.repositories.chat import chat_repository, message_repository
from app.repositories.customer import customer_repository
from app.repositories.product import product_repository


class ChatService:
    def __init__(self):
        self.chat_repository = chat_repository
        self.message_repository = message_repository
        self.customer_repository = customer_repository
        self.product_repository = product_repository

    def create_chat(
        self,
        db: Session,
        chat_data: ChatCreate,
        vendor_id: str
    ) -> ChatResponse:
        """Create a new chat between vendor and customer."""
        # Verify customer exists
        customer = self.customer_repository.get(db, chat_data.customer_id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Customer not found"
            )
        
        # Verify product exists if provided
        if chat_data.product_id:
            product = self.product_repository.get(db, chat_data.product_id)
            if not product:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Product not found"
                )
            
            # Check if product belongs to vendor
            if str(product.vendor_id) != vendor_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Product does not belong to this vendor"
                )
        
        # Check if chat already exists
        existing_chat = self.chat_repository.get_by_vendor_and_customer(
            db,
            vendor_id=vendor_id,
            customer_id=str(chat_data.customer_id),
            product_id=str(chat_data.product_id) if chat_data.product_id else None
        )
        
        if existing_chat:
            return self._build_chat_response(db, existing_chat)
        
        # Create new chat
        chat_dict = chat_data.model_dump()
        chat_dict["vendor_id"] = vendor_id
        
        db_chat = self.chat_repository.create(db, obj_in=chat_dict)
        return self._build_chat_response(db, db_chat)

    def get_chat(self, db: Session, chat_id: str) -> Optional[ChatWithMessages]:
        """Get chat with messages."""
        chat = self.chat_repository.get_with_relations(db, chat_id)
        if not chat:
            return None
        
        # Build response with messages
        chat_response = self._build_chat_response(db, chat)
        messages = [MessageResponse.model_validate(msg) for msg in chat.messages]
        
        return ChatWithMessages(
            **chat_response.model_dump(),
            messages=messages
        )

    def get_vendor_chats(
        self,
        db: Session,
        vendor_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> ChatListResponse:
        """Get all chats for a vendor."""
        chats = self.chat_repository.get_vendor_chats(
            db, vendor_id=vendor_id, skip=skip, limit=limit, status=status
        )
        
        total = self.chat_repository.count(
            db, filters={"vendor_id": vendor_id, "status": status} if status else {"vendor_id": vendor_id}
        )
        
        chat_responses = [self._build_chat_response(db, chat) for chat in chats]
        
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return ChatListResponse(
            chats=chat_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def get_customer_chats(
        self,
        db: Session,
        customer_id: str,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None
    ) -> ChatListResponse:
        """Get all chats for a customer."""
        chats = self.chat_repository.get_customer_chats(
            db, customer_id=customer_id, skip=skip, limit=limit, status=status
        )
        
        total = self.chat_repository.count(
            db, filters={"customer_id": customer_id, "status": status} if status else {"customer_id": customer_id}
        )
        
        chat_responses = [self._build_chat_response(db, chat) for chat in chats]
        
        pages = ceil(total / limit) if limit > 0 else 1
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return ChatListResponse(
            chats=chat_responses,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )

    def update_chat(
        self,
        db: Session,
        chat_id: str,
        chat_data: ChatUpdate,
        user_id: str,
        user_type: SenderType
    ) -> Optional[ChatResponse]:
        """Update chat (only participants can update)."""
        chat = self.chat_repository.get(db, chat_id)
        if not chat:
            return None
        
        # Check if user is participant
        if user_type == SenderType.VENDOR and str(chat.vendor_id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        elif user_type == SenderType.CUSTOMER and str(chat.customer_id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update chat
        update_dict = chat_data.model_dump(exclude_unset=True)
        updated_chat = self.chat_repository.update(db, db_obj=chat, obj_in=update_dict)
        
        return self._build_chat_response(db, updated_chat)

    def delete_chat(
        self,
        db: Session,
        chat_id: str,
        user_id: str,
        user_type: SenderType
    ) -> bool:
        """Soft delete chat (only participants can delete)."""
        chat = self.chat_repository.get(db, chat_id)
        if not chat:
            return False
        
        # Check if user is participant
        if user_type == SenderType.VENDOR and str(chat.vendor_id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        elif user_type == SenderType.CUSTOMER and str(chat.customer_id) != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        self.chat_repository.delete(db, id=chat_id)
        return True

    def _build_chat_response(self, db: Session, chat: Chat) -> ChatResponse:
        """Build chat response with additional data."""
        # Get latest message
        latest_message = self.message_repository.get_latest_message(db, chat_id=str(chat.id))
        
        return ChatResponse(
            id=chat.id,
            vendor_id=chat.vendor_id,
            customer_id=chat.customer_id,
            product_id=chat.product_id,
            status=chat.status,
            last_message_at=chat.last_message_at,
            created_at=chat.created_at,
            updated_at=chat.updated_at,
            vendor_name=chat.vendor.business_name if hasattr(chat, 'vendor') and chat.vendor else None,
            customer_name=chat.customer.name if hasattr(chat, 'customer') and chat.customer else None,
            product_name=chat.product.name if hasattr(chat, 'product') and chat.product else None,
            last_message=latest_message.content[:100] + "..." if latest_message and len(latest_message.content) > 100 else latest_message.content if latest_message else None
        )


# Create service instance
chat_service = ChatService()
