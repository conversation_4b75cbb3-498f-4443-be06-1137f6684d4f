import re
from typing import Optional
from enum import Enum


class CountryCode(str, Enum):
    TANZANIA = "TZ"
    KENYA = "KE"
    UGANDA = "UG"
    RWANDA = "RW"
    # Add more countries as needed


class PhoneValidator:
    """Phone number validation utility supporting multiple countries."""
    
    # Phone number patterns for different countries
    PATTERNS = {
        CountryCode.TANZANIA: {
            "pattern": r"^(\+255|0)(6[0-9]|7[0-9]|8[0-9])\d{7}$",
            "format_example": "+255712345678 or 0712345678",
            "description": "Tanzania mobile numbers (Vodacom, Airtel, Tigo, Halotel, etc.)"
        },
        CountryCode.KENYA: {
            "pattern": r"^(\+254|0)(7[0-9]|1[0-9])\d{7}$",
            "format_example": "+254712345678 or 0712345678",
            "description": "Kenya mobile numbers"
        },
        CountryCode.UGANDA: {
            "pattern": r"^(\+256|0)(7[0-9]|3[0-9])\d{7}$",
            "format_example": "+256712345678 or 0712345678",
            "description": "Uganda mobile numbers"
        },
        CountryCode.RWANDA: {
            "pattern": r"^(\+250|0)(7[0-9]|8[0-9])\d{7}$",
            "format_example": "+250712345678 or 0712345678",
            "description": "Rwanda mobile numbers"
        }
    }
    
    @classmethod
    def validate_phone(cls, phone: str, country: CountryCode = CountryCode.TANZANIA) -> bool:
        """
        Validate phone number for a specific country.
        
        Args:
            phone: Phone number to validate
            country: Country code for validation rules
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not phone or country not in cls.PATTERNS:
            return False
        
        # Remove spaces and special characters except + and digits
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        pattern = cls.PATTERNS[country]["pattern"]
        return bool(re.match(pattern, cleaned_phone))
    
    @classmethod
    def normalize_phone(cls, phone: str, country: CountryCode = CountryCode.TANZANIA) -> Optional[str]:
        """
        Normalize phone number to international format.
        
        Args:
            phone: Phone number to normalize
            country: Country code for normalization rules
            
        Returns:
            str: Normalized phone number or None if invalid
        """
        if not cls.validate_phone(phone, country):
            return None
        
        # Remove spaces and special characters except + and digits
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        # Convert to international format
        if country == CountryCode.TANZANIA:
            if cleaned_phone.startswith('0'):
                return '+255' + cleaned_phone[1:]
            elif cleaned_phone.startswith('+255'):
                return cleaned_phone
            elif cleaned_phone.startswith('255'):
                return '+' + cleaned_phone
        elif country == CountryCode.KENYA:
            if cleaned_phone.startswith('0'):
                return '+254' + cleaned_phone[1:]
            elif cleaned_phone.startswith('+254'):
                return cleaned_phone
            elif cleaned_phone.startswith('254'):
                return '+' + cleaned_phone
        elif country == CountryCode.UGANDA:
            if cleaned_phone.startswith('0'):
                return '+256' + cleaned_phone[1:]
            elif cleaned_phone.startswith('+256'):
                return cleaned_phone
            elif cleaned_phone.startswith('256'):
                return '+' + cleaned_phone
        elif country == CountryCode.RWANDA:
            if cleaned_phone.startswith('0'):
                return '+250' + cleaned_phone[1:]
            elif cleaned_phone.startswith('+250'):
                return cleaned_phone
            elif cleaned_phone.startswith('250'):
                return '+' + cleaned_phone
        
        return cleaned_phone
    
    @classmethod
    def get_validation_info(cls, country: CountryCode = CountryCode.TANZANIA) -> dict:
        """Get validation information for a country."""
        return cls.PATTERNS.get(country, {})
    
    @classmethod
    def detect_country(cls, phone: str) -> Optional[CountryCode]:
        """
        Detect country from phone number.
        
        Args:
            phone: Phone number to analyze
            
        Returns:
            CountryCode: Detected country or None
        """
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        # Check international prefixes
        if cleaned_phone.startswith('+255') or cleaned_phone.startswith('255'):
            return CountryCode.TANZANIA
        elif cleaned_phone.startswith('+254') or cleaned_phone.startswith('254'):
            return CountryCode.KENYA
        elif cleaned_phone.startswith('+256') or cleaned_phone.startswith('256'):
            return CountryCode.UGANDA
        elif cleaned_phone.startswith('+250') or cleaned_phone.startswith('250'):
            return CountryCode.RWANDA
        
        # For local numbers, try to validate against each country
        for country in CountryCode:
            if cls.validate_phone(phone, country):
                return country
        
        return None


# Convenience functions
def validate_phone(phone: str, country: CountryCode = CountryCode.TANZANIA) -> bool:
    """Validate phone number."""
    return PhoneValidator.validate_phone(phone, country)


def normalize_phone(phone: str, country: CountryCode = CountryCode.TANZANIA) -> Optional[str]:
    """Normalize phone number to international format."""
    return PhoneValidator.normalize_phone(phone, country)
