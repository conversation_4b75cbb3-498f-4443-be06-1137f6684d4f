from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # Database Configuration
    database_url: str
    
    # JWT Configuration
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Application Configuration
    app_name: str = "Dealer AI Backend"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # Supabase Configuration (for later milestones)
    supabase_url: Optional[str] = None
    supabase_key: Optional[str] = None
    
    # WhatsApp API Configuration (for later milestones)
    whatsapp_api_url: Optional[str] = None
    whatsapp_api_token: Optional[str] = None
    
    # AI Configuration (for later milestones)
    openrouter_api_key: Optional[str] = None
    
    # Payment Gateway Configuration (for later milestones)
    mpesa_consumer_key: Optional[str] = None
    mpesa_consumer_secret: Optional[str] = None
    selcom_api_key: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
