import os
import uuid
import shutil
from typing import Optional, List, Tuple
from pathlib import Path
from fastapi import UploadFile, HTTPException, status
import mimetypes
from datetime import datetime


class FileStorageService:
    """Local file storage service for handling uploads."""
    
    # Allowed file types and their extensions
    ALLOWED_TYPES = {
        "image": [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp"],
        "document": [".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt"],
        "audio": [".mp3", ".wav", ".ogg", ".m4a", ".aac"],
        "video": [".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm"],
        "archive": [".zip", ".rar", ".7z", ".tar", ".gz"]
    }
    
    # Maximum file sizes (in bytes)
    MAX_FILE_SIZES = {
        "image": 10 * 1024 * 1024,      # 10MB
        "document": 50 * 1024 * 1024,   # 50MB
        "audio": 100 * 1024 * 1024,     # 100MB
        "video": 500 * 1024 * 1024,     # 500MB
        "archive": 100 * 1024 * 1024    # 100MB
    }
    
    def __init__(self, base_upload_dir: str = "uploads"):
        self.base_upload_dir = Path(base_upload_dir)
        self.ensure_directories()
    
    def ensure_directories(self):
        """Create upload directories if they don't exist."""
        for file_type in self.ALLOWED_TYPES.keys():
            type_dir = self.base_upload_dir / file_type
            type_dir.mkdir(parents=True, exist_ok=True)
    
    def get_file_type(self, filename: str) -> Optional[str]:
        """Determine file type based on extension."""
        file_ext = Path(filename).suffix.lower()
        
        for file_type, extensions in self.ALLOWED_TYPES.items():
            if file_ext in extensions:
                return file_type
        
        return None
    
    def validate_file(self, file: UploadFile) -> Tuple[str, str]:
        """
        Validate uploaded file.
        
        Returns:
            Tuple of (file_type, error_message)
        """
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No filename provided"
            )
        
        # Check file type
        file_type = self.get_file_type(file.filename)
        if not file_type:
            allowed_extensions = []
            for extensions in self.ALLOWED_TYPES.values():
                allowed_extensions.extend(extensions)
            
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Allowed extensions: {', '.join(allowed_extensions)}"
            )
        
        # Check file size
        if hasattr(file, 'size') and file.size:
            max_size = self.MAX_FILE_SIZES.get(file_type, 10 * 1024 * 1024)
            if file.size > max_size:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File too large. Maximum size for {file_type} files: {max_size // (1024*1024)}MB"
                )
        
        return file_type, ""
    
    def generate_unique_filename(self, original_filename: str) -> str:
        """Generate a unique filename while preserving the extension."""
        file_ext = Path(original_filename).suffix.lower()
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{unique_id}{file_ext}"
    
    async def save_file(self, file: UploadFile) -> dict:
        """
        Save uploaded file to local storage.
        
        Returns:
            Dictionary with file information
        """
        # Validate file
        file_type, _ = self.validate_file(file)
        
        # Generate unique filename
        unique_filename = self.generate_unique_filename(file.filename)
        
        # Create file path
        file_path = self.base_upload_dir / file_type / unique_filename
        
        try:
            # Save file
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # Get file size
            file_size = file_path.stat().st_size
            
            # Generate URL (relative path)
            file_url = f"/uploads/{file_type}/{unique_filename}"
            
            return {
                "filename": file.filename,
                "unique_filename": unique_filename,
                "url": file_url,
                "file_type": file_type,
                "file_size": file_size,
                "mime_type": mimetypes.guess_type(file.filename)[0]
            }
            
        except Exception as e:
            # Clean up file if it was created
            if file_path.exists():
                file_path.unlink()
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to save file: {str(e)}"
            )
    
    async def save_multiple_files(self, files: List[UploadFile]) -> List[dict]:
        """Save multiple files and return their information."""
        if len(files) > 10:  # Limit number of files
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Too many files. Maximum 10 files allowed per upload."
            )
        
        saved_files = []
        
        for file in files:
            try:
                file_info = await self.save_file(file)
                saved_files.append(file_info)
            except HTTPException:
                # Clean up already saved files
                for saved_file in saved_files:
                    self.delete_file(saved_file["url"])
                raise
        
        return saved_files
    
    def delete_file(self, file_url: str) -> bool:
        """Delete a file by its URL."""
        try:
            # Convert URL to file path
            # Remove leading slash and convert to path
            relative_path = file_url.lstrip("/")
            file_path = Path(relative_path)
            
            if file_path.exists():
                file_path.unlink()
                return True
            
            return False
            
        except Exception:
            return False
    
    def get_file_path(self, file_url: str) -> Optional[Path]:
        """Get the actual file path from URL."""
        try:
            relative_path = file_url.lstrip("/")
            file_path = Path(relative_path)
            
            if file_path.exists():
                return file_path
            
            return None
            
        except Exception:
            return None
    
    def cleanup_orphaned_files(self, referenced_urls: List[str]) -> int:
        """
        Clean up files that are no longer referenced.
        
        Args:
            referenced_urls: List of file URLs that are still in use
            
        Returns:
            Number of files deleted
        """
        deleted_count = 0
        
        for file_type in self.ALLOWED_TYPES.keys():
            type_dir = self.base_upload_dir / file_type
            
            if not type_dir.exists():
                continue
            
            for file_path in type_dir.iterdir():
                if file_path.is_file():
                    file_url = f"/uploads/{file_type}/{file_path.name}"
                    
                    if file_url not in referenced_urls:
                        try:
                            file_path.unlink()
                            deleted_count += 1
                        except Exception:
                            pass
        
        return deleted_count


# Create global instance
file_storage = FileStorageService()
