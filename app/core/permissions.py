from typing import List
from fastapi import Depends, HTTPException, status
from app.core.auth import get_current_vendor
from app.models.vendor import Vend<PERSON>, VendorRole


def require_roles(allowed_roles: List[VendorRole]):
    """
    Decorator factory to require specific roles for endpoint access.
    
    Args:
        allowed_roles: List of roles that are allowed to access the endpoint
    
    Returns:
        Dependency function that checks if current vendor has required role
    """
    def role_checker(current_vendor: Vendor = Depends(get_current_vendor)) -> Vendor:
        if current_vendor.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {[role.value for role in allowed_roles]}"
            )
        return current_vendor
    
    return role_checker


def require_admin(current_vendor: Vendor = Depends(get_current_vendor)) -> Vendor:
    """Require admin role for endpoint access."""
    if current_vendor.role != VendorRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied. Admin role required."
        )
    return current_vendor


def require_vendor_or_admin(current_vendor: Vendor = Depends(get_current_vendor)) -> Vendor:
    """Require vendor or admin role for endpoint access."""
    if current_vendor.role not in [VendorRole.VENDOR, VendorRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied. Vendor or admin role required."
        )
    return current_vendor


def check_vendor_ownership_or_admin(
    resource_vendor_id: str,
    current_vendor: Vendor = Depends(get_current_vendor)
) -> Vendor:
    """
    Check if current vendor owns the resource or is an admin.
    
    Args:
        resource_vendor_id: The vendor ID that owns the resource
        current_vendor: Current authenticated vendor
    
    Returns:
        Current vendor if authorized
    
    Raises:
        HTTPException: If vendor doesn't own resource and is not admin
    """
    if current_vendor.role == VendorRole.ADMIN:
        return current_vendor
    
    if str(current_vendor.id) != resource_vendor_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied. You can only access your own resources."
        )
    
    return current_vendor


def is_admin(vendor: Vendor) -> bool:
    """Check if vendor has admin role."""
    return vendor.role == VendorRole.ADMIN


def is_vendor_or_admin(vendor: Vendor) -> bool:
    """Check if vendor has vendor or admin role."""
    return vendor.role in [VendorRole.VENDOR, VendorRole.ADMIN]


def can_modify_vendor_role(current_vendor: Vendor, target_vendor: Vendor) -> bool:
    """
    Check if current vendor can modify target vendor's role.
    Only admins can modify roles, and they can't demote themselves.
    """
    if current_vendor.role != VendorRole.ADMIN:
        return False
    
    # Admins can't demote themselves
    if current_vendor.id == target_vendor.id:
        return False
    
    return True
