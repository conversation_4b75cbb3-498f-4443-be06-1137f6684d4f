from typing import List, Optional, Dict, Any
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_vendor
from app.models.vendor import Vendor
from app.services.monetization import subscription_service
from app.schemas.monetization import SubscriptionStatusResponse


class SubscriptionValidator:
    """Validator for subscription-based access control."""
    
    @staticmethod
    def require_active_subscription(
        current_vendor: Vendor = Depends(get_current_vendor),
        db: Session = Depends(get_db)
    ) -> Vendor:
        """Require vendor to have an active subscription."""
        subscription_status = subscription_service.check_subscription_status(
            db, str(current_vendor.id)
        )
        
        if not subscription_status.has_active_subscription:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Active subscription required to access this feature"
            )
        
        return current_vendor
    
    @staticmethod
    def require_plan_feature(feature_name: str):
        """
        Decorator factory to require specific plan features.
        
        Args:
            feature_name: Name of the feature to check (e.g., 'chat_support', 'analytics_access')
        """
        def feature_checker(
            current_vendor: Vendor = Depends(get_current_vendor),
            db: Session = Depends(get_db)
        ) -> Vendor:
            subscription_status = subscription_service.check_subscription_status(
                db, str(current_vendor.id)
            )
            
            if not subscription_status.has_active_subscription:
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail="Active subscription required to access this feature"
                )
            
            plan_limits = subscription_status.plan_limits or {}
            if not plan_limits.get(feature_name, False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Your current plan does not include {feature_name.replace('_', ' ')}"
                )
            
            return current_vendor
        
        return feature_checker
    
    @staticmethod
    def check_usage_limit(limit_type: str, requested_amount: int = 1):
        """
        Decorator factory to check usage limits.
        
        Args:
            limit_type: Type of limit to check ('products', 'file_storage_mb')
            requested_amount: Amount being requested (default: 1)
        """
        def limit_checker(
            current_vendor: Vendor = Depends(get_current_vendor),
            db: Session = Depends(get_db)
        ) -> Dict[str, Any]:
            subscription_status = subscription_service.check_subscription_status(
                db, str(current_vendor.id)
            )
            
            if not subscription_status.has_active_subscription:
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail="Active subscription required to access this feature"
                )
            
            plan_limits = subscription_status.plan_limits or {}
            usage_stats = subscription_status.usage_stats or {}
            
            if limit_type == "products":
                max_products = plan_limits.get("max_products", 0)
                current_products = usage_stats.get("current_products_count", 0)
                
                if current_products + requested_amount > max_products:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Product limit exceeded. Your plan allows {max_products} products, "
                               f"you currently have {current_products}"
                    )
            
            elif limit_type == "file_storage_mb":
                max_storage = plan_limits.get("max_file_uploads_mb", 0)
                current_usage = usage_stats.get("current_file_usage_mb", 0)
                
                if current_usage + requested_amount > max_storage:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"File storage limit exceeded. Your plan allows {max_storage}MB, "
                               f"you currently use {current_usage:.2f}MB"
                    )
            
            return {
                "vendor": current_vendor,
                "subscription_status": subscription_status
            }
        
        return limit_checker
    
    @staticmethod
    def get_subscription_info(
        current_vendor: Vendor = Depends(get_current_vendor),
        db: Session = Depends(get_db)
    ) -> SubscriptionStatusResponse:
        """Get subscription information for the current vendor."""
        return subscription_service.check_subscription_status(db, str(current_vendor.id))


# Convenience functions for common checks
def require_active_subscription(
    current_vendor: Vendor = Depends(get_current_vendor),
    db: Session = Depends(get_db)
) -> Vendor:
    """Require vendor to have an active subscription."""
    return SubscriptionValidator.require_active_subscription(current_vendor, db)


def require_chat_support(
    current_vendor: Vendor = Depends(get_current_vendor),
    db: Session = Depends(get_db)
) -> Vendor:
    """Require chat support feature."""
    return SubscriptionValidator.require_plan_feature("chat_support")(current_vendor, db)


def require_analytics_access(
    current_vendor: Vendor = Depends(get_current_vendor),
    db: Session = Depends(get_db)
) -> Vendor:
    """Require analytics access feature."""
    return SubscriptionValidator.require_plan_feature("analytics_access")(current_vendor, db)


def require_priority_listing(
    current_vendor: Vendor = Depends(get_current_vendor),
    db: Session = Depends(get_db)
) -> Vendor:
    """Require priority listing feature."""
    return SubscriptionValidator.require_plan_feature("priority_listing")(current_vendor, db)


def check_product_limit(
    requested_products: int = 1,
    current_vendor: Vendor = Depends(get_current_vendor),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Check if vendor can add more products."""
    return SubscriptionValidator.check_usage_limit("products", requested_products)(current_vendor, db)


def check_file_storage_limit(
    requested_mb: float,
    current_vendor: Vendor = Depends(get_current_vendor),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Check if vendor can upload more files."""
    return SubscriptionValidator.check_usage_limit("file_storage_mb", int(requested_mb))(current_vendor, db)


def get_subscription_info(
    current_vendor: Vendor = Depends(get_current_vendor),
    db: Session = Depends(get_db)
) -> SubscriptionStatusResponse:
    """Get subscription information for the current vendor."""
    return SubscriptionValidator.get_subscription_info(current_vendor, db)
