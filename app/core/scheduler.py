import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.monetization import subscription_service
from app.repositories.monetization import subscription_repository

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SchedulerService:
    """Service for managing scheduled tasks."""
    
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.scheduler.start()
        logger.info("Scheduler started")
    
    def start(self):
        """Start the scheduler and add jobs."""
        if not self.scheduler.running:
            self.scheduler.start()
            logger.info("Scheduler started")
        
        # Add scheduled jobs
        self.add_subscription_expiry_job()
        self.add_subscription_renewal_reminder_job()
        self.add_usage_stats_update_job()
        
        logger.info("All scheduled jobs added")
    
    def stop(self):
        """Stop the scheduler."""
        if self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("Scheduler stopped")
    
    def add_subscription_expiry_job(self):
        """Add job to process expired subscriptions."""
        self.scheduler.add_job(
            func=self.process_expired_subscriptions,
            trigger=CronTrigger(hour=0, minute=0),  # Run daily at midnight
            id='process_expired_subscriptions',
            name='Process Expired Subscriptions',
            replace_existing=True
        )
        logger.info("Added subscription expiry job")
    
    def add_subscription_renewal_reminder_job(self):
        """Add job to send renewal reminders."""
        self.scheduler.add_job(
            func=self.send_renewal_reminders,
            trigger=CronTrigger(hour=9, minute=0),  # Run daily at 9 AM
            id='send_renewal_reminders',
            name='Send Subscription Renewal Reminders',
            replace_existing=True
        )
        logger.info("Added renewal reminder job")
    
    def add_usage_stats_update_job(self):
        """Add job to update subscription usage statistics."""
        self.scheduler.add_job(
            func=self.update_usage_statistics,
            trigger=IntervalTrigger(hours=6),  # Run every 6 hours
            id='update_usage_statistics',
            name='Update Subscription Usage Statistics',
            replace_existing=True
        )
        logger.info("Added usage stats update job")
    
    def process_expired_subscriptions(self):
        """Process expired subscriptions."""
        try:
            db: Session = SessionLocal()
            count = subscription_service.process_expired_subscriptions(db)
            logger.info(f"Processed {count} expired subscriptions")
            db.close()
        except Exception as e:
            logger.error(f"Error processing expired subscriptions: {str(e)}")
    
    def send_renewal_reminders(self):
        """Send renewal reminders for subscriptions expiring soon."""
        try:
            db: Session = SessionLocal()
            
            # Get subscriptions expiring in the next 7 days
            expiring_subscriptions = subscription_repository.get_expiring_subscriptions(
                db, days_ahead=7
            )
            
            for subscription in expiring_subscriptions:
                # Here you would integrate with email/SMS service
                # For now, just log the reminder
                logger.info(
                    f"Renewal reminder: Subscription {subscription.id} for vendor "
                    f"{subscription.vendor_id} expires on {subscription.end_date}"
                )
            
            logger.info(f"Sent renewal reminders for {len(expiring_subscriptions)} subscriptions")
            db.close()
            
        except Exception as e:
            logger.error(f"Error sending renewal reminders: {str(e)}")
    
    def update_usage_statistics(self):
        """Update subscription usage statistics."""
        try:
            db: Session = SessionLocal()
            
            # Get all active subscriptions
            active_subscriptions = db.query(subscription_repository.model).filter(
                subscription_repository.model.status == "active",
                subscription_repository.model.is_deleted == False
            ).all()
            
            updated_count = 0
            for subscription in active_subscriptions:
                try:
                    # Count vendor's products
                    from app.repositories.product import product_repository
                    products_count = product_repository.count(
                        db, filters={"vendor_id": str(subscription.vendor_id)}
                    )
                    
                    # Calculate file usage (this would need to be implemented based on your file tracking)
                    # For now, we'll just update the products count
                    subscription_repository.update_usage_stats(
                        db,
                        subscription_id=str(subscription.id),
                        products_count=products_count
                    )
                    updated_count += 1
                    
                except Exception as e:
                    logger.error(f"Error updating stats for subscription {subscription.id}: {str(e)}")
            
            logger.info(f"Updated usage statistics for {updated_count} subscriptions")
            db.close()
            
        except Exception as e:
            logger.error(f"Error updating usage statistics: {str(e)}")
    
    def add_custom_job(self, func, trigger, job_id, name, **kwargs):
        """Add a custom scheduled job."""
        self.scheduler.add_job(
            func=func,
            trigger=trigger,
            id=job_id,
            name=name,
            replace_existing=True,
            **kwargs
        )
        logger.info(f"Added custom job: {name}")
    
    def remove_job(self, job_id):
        """Remove a scheduled job."""
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Removed job: {job_id}")
        except Exception as e:
            logger.error(f"Error removing job {job_id}: {str(e)}")
    
    def list_jobs(self):
        """List all scheduled jobs."""
        jobs = self.scheduler.get_jobs()
        job_info = []
        for job in jobs:
            job_info.append({
                "id": job.id,
                "name": job.name,
                "next_run": job.next_run_time,
                "trigger": str(job.trigger)
            })
        return job_info
    
    def pause_job(self, job_id):
        """Pause a scheduled job."""
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"Paused job: {job_id}")
        except Exception as e:
            logger.error(f"Error pausing job {job_id}: {str(e)}")
    
    def resume_job(self, job_id):
        """Resume a paused job."""
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"Resumed job: {job_id}")
        except Exception as e:
            logger.error(f"Error resuming job {job_id}: {str(e)}")


# Global scheduler instance
scheduler_service = SchedulerService()
