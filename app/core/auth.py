from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_token
from app.repositories.vendor import vendor_repository
from app.models.vendor import Vendor

# HTTP Bearer token scheme
security = HTTPBearer()


async def get_current_vendor(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Vendor:
    """Get current authenticated vendor from JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # Verify token
    payload = verify_token(credentials.credentials)
    if payload is None:
        raise credentials_exception
    
    # Get vendor ID from token
    vendor_id: str = payload.get("sub")
    if vendor_id is None:
        raise credentials_exception
    
    # Get vendor from database
    vendor = vendor_repository.get(db, vendor_id)
    if vendor is None:
        raise credentials_exception
    
    # Check if vendor is active
    if not vendor_repository.is_active(vendor):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive vendor account"
        )
    
    return vendor


async def get_current_active_vendor(
    current_vendor: Vendor = Depends(get_current_vendor)
) -> Vendor:
    """Get current active vendor (alias for get_current_vendor)."""
    return current_vendor


def get_optional_current_vendor(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[Vendor]:
    """Get current vendor if token is provided, otherwise return None."""
    if not credentials:
        return None
    
    try:
        # Verify token
        payload = verify_token(credentials.credentials)
        if payload is None:
            return None
        
        # Get vendor ID from token
        vendor_id: str = payload.get("sub")
        if vendor_id is None:
            return None
        
        # Get vendor from database
        vendor = vendor_repository.get(db, vendor_id)
        if vendor is None or not vendor_repository.is_active(vendor):
            return None
        
        return vendor
    except Exception:
        return None
