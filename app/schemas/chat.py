from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from app.models.chat import ChatStatus, SenderType, MessageStatus


class AttachmentBase(BaseModel):
    filename: str
    url: str
    file_type: str  # image, document, audio, etc.
    file_size: Optional[int] = None


class MessageBase(BaseModel):
    content: str = Field(..., min_length=1, max_length=5000)
    attachments: Optional[List[AttachmentBase]] = []


class MessageCreate(MessageBase):
    pass


class MessageUpdate(BaseModel):
    content: Optional[str] = Field(None, min_length=1, max_length=5000)
    status: Optional[MessageStatus] = None


class MessageResponse(MessageBase):
    id: UUID
    chat_id: UUID
    sender_type: SenderType
    sender_id: UUID
    status: MessageStatus
    whatsapp_message_id: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ChatBase(BaseModel):
    product_id: Optional[UUID] = None


class ChatCreate(ChatBase):
    customer_id: UUID


class ChatUpdate(BaseModel):
    status: Optional[ChatStatus] = None


class ChatResponse(ChatBase):
    id: UUID
    vendor_id: UUID
    customer_id: UUID
    status: ChatStatus
    last_message_at: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Optional nested data
    vendor_name: Optional[str] = None
    customer_name: Optional[str] = None
    product_name: Optional[str] = None
    last_message: Optional[str] = None
    unread_count: Optional[int] = 0

    class Config:
        from_attributes = True


class ChatWithMessages(ChatResponse):
    messages: List[MessageResponse] = []

    class Config:
        from_attributes = True


class ChatListResponse(BaseModel):
    chats: List[ChatResponse]
    total: int
    page: int
    size: int
    pages: int


class MessageListResponse(BaseModel):
    messages: List[MessageResponse]
    total: int
    page: int
    size: int
    pages: int
