from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, List
from datetime import datetime
from uuid import UUID
from decimal import Decimal
from app.models.product import ProductStatus


class ProductImageBase(BaseModel):
    image_url: str = Field(..., max_length=500)
    is_primary: bool = False


class ProductImageCreate(ProductImageBase):
    pass


class ProductImageResponse(ProductImageBase):
    id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class ProductVariantBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    value: str = Field(..., min_length=1, max_length=255)
    price_adjustment: Decimal = Field(default=0.00, ge=0)
    stock_quantity: int = Field(default=0, ge=0)
    is_active: bool = True


class ProductVariantCreate(ProductVariantBase):
    pass


class ProductVariantUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    value: Optional[str] = Field(None, min_length=1, max_length=255)
    price_adjustment: Optional[Decimal] = Field(None, ge=0)
    stock_quantity: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None


class ProductVariantResponse(ProductVariantBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ProductBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    price: Decimal = Field(..., gt=0, decimal_places=2)
    category_id: UUID
    status: ProductStatus = ProductStatus.ACTIVE
    is_featured: bool = False


class ProductCreate(ProductBase):
    images: Optional[List[ProductImageCreate]] = []
    variants: Optional[List[ProductVariantCreate]] = []


class ProductUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    category_id: Optional[UUID] = None
    status: Optional[ProductStatus] = None
    is_featured: Optional[bool] = None


class ProductInDB(ProductBase):
    id: UUID
    vendor_id: UUID
    is_deleted: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ProductResponse(ProductInDB):
    images: List[ProductImageResponse] = []
    variants: List[ProductVariantResponse] = []

    class Config:
        from_attributes = True


class ProductListResponse(BaseModel):
    id: UUID
    name: str
    description: Optional[str] = None
    price: Decimal
    status: ProductStatus
    is_featured: bool
    vendor_id: UUID
    category_id: UUID
    created_at: datetime
    primary_image: Optional[str] = None  # URL of primary image

    class Config:
        from_attributes = True


class ProductsListResponse(BaseModel):
    products: List[ProductListResponse]
    total: int
    page: int
    size: int
    pages: int
