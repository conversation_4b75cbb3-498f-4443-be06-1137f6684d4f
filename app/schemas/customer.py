from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional
from datetime import datetime
from uuid import UUID
from app.models.customer import CustomerStatus
from app.core.phone_validation import validate_phone, normalize_phone, CountryCode


class CustomerBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    phone: str = Field(..., min_length=10, max_length=20, description="Phone number (Tanzania format: +255XXXXXXXXX or 0XXXXXXXXX)")
    email: Optional[EmailStr] = None
    location: Optional[str] = Field(None, max_length=255)

    @field_validator('phone')
    @classmethod
    def validate_phone_number(cls, v):
        if not validate_phone(v, CountryCode.TANZANIA):
            raise ValueError('Invalid phone number format. Use Tanzania format: +255XXXXXXXXX or 0XXXXXXXXX')
        return normalize_phone(v, CountryCode.TANZANIA)


class CustomerCreate(CustomerBase):
    pass


class CustomerUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    phone: Optional[str] = Field(None, min_length=10, max_length=20, description="Phone number (Tanzania format)")
    email: Optional[EmailStr] = None
    location: Optional[str] = Field(None, max_length=255)
    status: Optional[CustomerStatus] = None

    @field_validator('phone')
    @classmethod
    def validate_phone_number(cls, v):
        if v is not None and not validate_phone(v, CountryCode.TANZANIA):
            raise ValueError('Invalid phone number format. Use Tanzania format: +255XXXXXXXXX or 0XXXXXXXXX')
        return normalize_phone(v, CountryCode.TANZANIA) if v else v


class CustomerInDB(CustomerBase):
    id: UUID
    status: CustomerStatus
    is_deleted: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Customer(CustomerInDB):
    pass


class CustomerResponse(BaseModel):
    id: UUID
    name: str
    phone: str
    email: Optional[str] = None
    location: Optional[str] = None
    status: CustomerStatus
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class CustomerListResponse(BaseModel):
    customers: list[CustomerResponse]
    total: int
    page: int
    size: int
    pages: int
