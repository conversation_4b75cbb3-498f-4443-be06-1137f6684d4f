from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from uuid import UUID
from app.models.monetization import (
    PlanType, PlanDuration, SubscriptionStatus, PaymentStatus, 
    PaymentMethod, PurchaseType
)


# Plan Schemas
class PlanBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    plan_type: PlanType
    duration: PlanDuration
    price: Decimal = Field(..., ge=0, decimal_places=2)
    currency: str = Field(default="TZS", min_length=3, max_length=3)
    features: Optional[Dict[str, Any]] = None
    max_products: int = Field(default=10, ge=0)
    max_images_per_product: int = Field(default=5, ge=1)
    max_file_uploads_mb: int = Field(default=100, ge=0)
    chat_support: bool = False
    analytics_access: bool = False
    priority_listing: bool = False
    is_active: bool = True


class PlanCreate(PlanBase):
    pass


class PlanUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    features: Optional[Dict[str, Any]] = None
    max_products: Optional[int] = Field(None, ge=0)
    max_images_per_product: Optional[int] = Field(None, ge=1)
    max_file_uploads_mb: Optional[int] = Field(None, ge=0)
    chat_support: Optional[bool] = None
    analytics_access: Optional[bool] = None
    priority_listing: Optional[bool] = None
    is_active: Optional[bool] = None


class PlanResponse(PlanBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Subscription Schemas
class SubscriptionBase(BaseModel):
    plan_id: UUID
    auto_renew: bool = True


class SubscriptionCreate(SubscriptionBase):
    pass


class SubscriptionUpdate(BaseModel):
    auto_renew: Optional[bool] = None
    status: Optional[SubscriptionStatus] = None


class SubscriptionResponse(BaseModel):
    id: UUID
    vendor_id: UUID
    plan_id: UUID
    status: SubscriptionStatus
    start_date: datetime
    end_date: datetime
    auto_renew: bool
    current_products_count: int
    current_file_usage_mb: Decimal
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Nested data
    plan: Optional[PlanResponse] = None

    class Config:
        from_attributes = True


# In-App Purchase Schemas
class InAppPurchaseBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    price: Decimal = Field(..., ge=0, decimal_places=2)
    currency: str = Field(default="TZS", min_length=3, max_length=3)
    benefits: Optional[Dict[str, Any]] = None
    product_boost_days: int = Field(default=0, ge=0)
    featured_listing_days: int = Field(default=0, ge=0)
    extra_product_slots: int = Field(default=0, ge=0)
    extra_file_storage_mb: int = Field(default=0, ge=0)
    is_active: bool = True


class InAppPurchaseCreate(InAppPurchaseBase):
    pass


class InAppPurchaseUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    benefits: Optional[Dict[str, Any]] = None
    product_boost_days: Optional[int] = Field(None, ge=0)
    featured_listing_days: Optional[int] = Field(None, ge=0)
    extra_product_slots: Optional[int] = Field(None, ge=0)
    extra_file_storage_mb: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None


class InAppPurchaseResponse(InAppPurchaseBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Purchase Schemas
class PurchaseBase(BaseModel):
    purchase_type: PurchaseType
    amount: Decimal = Field(..., ge=0, decimal_places=2)
    currency: str = Field(default="TZS", min_length=3, max_length=3)
    metadata: Optional[Dict[str, Any]] = None


class PurchaseCreate(PurchaseBase):
    subscription_id: Optional[UUID] = None
    in_app_purchase_id: Optional[UUID] = None

    @validator('subscription_id', 'in_app_purchase_id')
    def validate_purchase_reference(cls, v, values):
        purchase_type = values.get('purchase_type')
        if purchase_type == PurchaseType.SUBSCRIPTION and not values.get('subscription_id'):
            raise ValueError('subscription_id is required for subscription purchases')
        if purchase_type == PurchaseType.IN_APP_PURCHASE and not values.get('in_app_purchase_id'):
            raise ValueError('in_app_purchase_id is required for in-app purchases')
        return v


class PurchaseResponse(PurchaseBase):
    id: UUID
    vendor_id: UUID
    subscription_id: Optional[UUID] = None
    in_app_purchase_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Nested data
    subscription: Optional[SubscriptionResponse] = None
    in_app_purchase: Optional[InAppPurchaseResponse] = None

    class Config:
        from_attributes = True


# Payment Schemas
class PaymentBase(BaseModel):
    amount: Decimal = Field(..., ge=0, decimal_places=2)
    currency: str = Field(default="TZS", min_length=3, max_length=3)
    payment_method: PaymentMethod
    phone_number: Optional[str] = Field(None, min_length=10, max_length=20)
    account_number: Optional[str] = Field(None, max_length=100)


class PaymentCreate(PaymentBase):
    purchase_id: UUID


class PaymentUpdate(BaseModel):
    status: Optional[PaymentStatus] = None
    external_reference: Optional[str] = Field(None, max_length=255)
    gateway_response: Optional[Dict[str, Any]] = None
    admin_notes: Optional[str] = None


class PaymentResponse(PaymentBase):
    id: UUID
    purchase_id: UUID
    status: PaymentStatus
    external_reference: Optional[str] = None
    gateway_response: Optional[Dict[str, Any]] = None
    paid_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    admin_notes: Optional[str] = None
    processed_by: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# List Response Schemas
class PlanListResponse(BaseModel):
    plans: List[PlanResponse]
    total: int
    page: int
    size: int
    pages: int


class SubscriptionListResponse(BaseModel):
    subscriptions: List[SubscriptionResponse]
    total: int
    page: int
    size: int
    pages: int


class InAppPurchaseListResponse(BaseModel):
    in_app_purchases: List[InAppPurchaseResponse]
    total: int
    page: int
    size: int
    pages: int


class PurchaseListResponse(BaseModel):
    purchases: List[PurchaseResponse]
    total: int
    page: int
    size: int
    pages: int


class PaymentListResponse(BaseModel):
    payments: List[PaymentResponse]
    total: int
    page: int
    size: int
    pages: int


# Subscription Status Check Schema
class SubscriptionStatusResponse(BaseModel):
    has_active_subscription: bool
    current_subscription: Optional[SubscriptionResponse] = None
    plan_limits: Optional[Dict[str, Any]] = None
    usage_stats: Optional[Dict[str, Any]] = None
