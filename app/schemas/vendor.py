from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime
from uuid import UUID
from app.models.vendor import VendorStatus


class VendorBase(BaseModel):
    business_name: str = Field(..., min_length=1, max_length=255)
    owner_name: str = Field(..., min_length=1, max_length=255)
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=20)
    description: Optional[str] = None
    location: Optional[str] = Field(None, max_length=255)


class VendorCreate(VendorBase):
    password: str = Field(..., min_length=8, max_length=100)


class VendorUpdate(BaseModel):
    business_name: Optional[str] = Field(None, min_length=1, max_length=255)
    owner_name: Optional[str] = Field(None, min_length=1, max_length=255)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, min_length=10, max_length=20)
    description: Optional[str] = None
    location: Optional[str] = Field(None, max_length=255)
    status: Optional[VendorStatus] = None


class VendorInDB(VendorBase):
    id: UUID
    status: VendorStatus
    is_deleted: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Vendor(VendorInDB):
    pass


class VendorLogin(BaseModel):
    email: EmailStr
    password: str


class VendorResponse(BaseModel):
    id: UUID
    business_name: str
    owner_name: str
    email: str
    phone: str
    description: Optional[str] = None
    location: Optional[str] = None
    status: VendorStatus
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    vendor_id: Optional[str] = None
