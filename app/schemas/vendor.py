from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional, List
from datetime import datetime
from uuid import UUID
from app.models.vendor import Vendor<PERSON>tat<PERSON>, VendorRole
from app.core.phone_validation import validate_phone, normalize_phone, CountryCode


class VendorBase(BaseModel):
    business_name: str = Field(..., min_length=1, max_length=255)
    owner_name: str = Field(..., min_length=1, max_length=255)
    email: Optional[EmailStr] = None  # Made optional
    phone: str = Field(..., min_length=10, max_length=20, description="Phone number (Tanzania format: +255XXXXXXXXX or 0XXXXXXXXX)")
    description: Optional[str] = None
    location: Optional[str] = Field(None, max_length=255)

    @field_validator('phone')
    @classmethod
    def validate_phone_number(cls, v):
        if not validate_phone(v, CountryCode.TANZANIA):
            raise ValueError('Invalid phone number format. Use Tanzania format: +255XXXXXXXXX or 0XXXXXXXXX')
        return normalize_phone(v, CountryCode.TANZANIA)


class VendorCreate(VendorBase):
    password: str = Field(..., min_length=8, max_length=100)


class VendorUpdate(BaseModel):
    business_name: Optional[str] = Field(None, min_length=1, max_length=255)
    owner_name: Optional[str] = Field(None, min_length=1, max_length=255)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, min_length=10, max_length=20, description="Phone number (Tanzania format)")
    description: Optional[str] = None
    location: Optional[str] = Field(None, max_length=255)
    status: Optional[VendorStatus] = None
    role: Optional[VendorRole] = None  # Only admins can change roles

    @field_validator('phone')
    @classmethod
    def validate_phone_number(cls, v):
        if v is not None and not validate_phone(v, CountryCode.TANZANIA):
            raise ValueError('Invalid phone number format. Use Tanzania format: +255XXXXXXXXX or 0XXXXXXXXX')
        return normalize_phone(v, CountryCode.TANZANIA) if v else v


class VendorInDB(VendorBase):
    id: UUID
    status: VendorStatus
    role: VendorRole
    is_deleted: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Vendor(VendorInDB):
    pass


class VendorLogin(BaseModel):
    phone: str = Field(..., description="Phone number (Tanzania format)")
    password: str

    @field_validator('phone')
    @classmethod
    def validate_phone_number(cls, v):
        if not validate_phone(v, CountryCode.TANZANIA):
            raise ValueError('Invalid phone number format. Use Tanzania format: +255XXXXXXXXX or 0XXXXXXXXX')
        return normalize_phone(v, CountryCode.TANZANIA)


class VendorResponse(BaseModel):
    id: UUID
    business_name: str
    owner_name: str
    email: Optional[str] = None
    phone: str
    description: Optional[str] = None
    location: Optional[str] = None
    status: VendorStatus
    role: VendorRole
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    vendor_id: Optional[str] = None
