from sqlalchemy import Column, String, Text, Boolean, DateTime, Numeric, Integer, ForeignKey, Enum, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from app.core.database import Base


class PlanType(str, enum.Enum):
    FREE = "free"
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"


class PlanDuration(str, enum.Enum):
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    LIFETIME = "lifetime"


class SubscriptionStatus(str, enum.Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    SUSPENDED = "suspended"


class PaymentStatus(str, enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class PaymentMethod(str, enum.Enum):
    MPESA = "mpesa"
    SELCOM = "selcom"
    BANK_TRANSFER = "bank_transfer"
    CASH = "cash"
    MANUAL = "manual"  # For admin manual payments


class PurchaseType(str, enum.Enum):
    SUBSCRIPTION = "subscription"
    IN_APP_PURCHASE = "in_app_purchase"


class Plan(Base):
    __tablename__ = "plans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, unique=True)
    description = Column(Text)
    plan_type = Column(Enum(PlanType), nullable=False)
    duration = Column(Enum(PlanDuration), nullable=False)
    price = Column(Numeric(10, 2), nullable=False, default=0.00)
    currency = Column(String(3), nullable=False, default="TZS")  # TZS, USD, etc.
    
    # Plan features (JSON for flexibility)
    features = Column(JSON, nullable=True)  # {"max_products": 100, "chat_support": true, etc.}
    
    # Plan limits
    max_products = Column(Integer, default=10)
    max_images_per_product = Column(Integer, default=5)
    max_file_uploads_mb = Column(Integer, default=100)  # Total MB per month
    chat_support = Column(Boolean, default=False)
    analytics_access = Column(Boolean, default=False)
    priority_listing = Column(Boolean, default=False)
    
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    subscriptions = relationship("Subscription", back_populates="plan")
    
    def __repr__(self):
        return f"<Plan(id={self.id}, name='{self.name}', type={self.plan_type})>"


class Subscription(Base):
    __tablename__ = "subscriptions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    vendor_id = Column(UUID(as_uuid=True), ForeignKey("vendors.id"), nullable=False, index=True)
    plan_id = Column(UUID(as_uuid=True), ForeignKey("plans.id"), nullable=False, index=True)
    
    status = Column(Enum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE, nullable=False)
    start_date = Column(DateTime(timezone=True), server_default=func.now())
    end_date = Column(DateTime(timezone=True), nullable=False)
    auto_renew = Column(Boolean, default=True, nullable=False)
    
    # Usage tracking
    current_products_count = Column(Integer, default=0)
    current_file_usage_mb = Column(Numeric(10, 2), default=0.00)
    
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    vendor = relationship("Vendor", backref="subscriptions")
    plan = relationship("Plan", back_populates="subscriptions")
    purchases = relationship("Purchase", back_populates="subscription")
    
    def __repr__(self):
        return f"<Subscription(id={self.id}, vendor_id={self.vendor_id}, status={self.status})>"


class InAppPurchase(Base):
    __tablename__ = "in_app_purchases"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    price = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default="TZS")
    
    # Purchase benefits (JSON for flexibility)
    benefits = Column(JSON, nullable=True)  # {"product_boost_days": 7, "featured_listing": true}
    
    # Specific benefits
    product_boost_days = Column(Integer, default=0)  # Boost product visibility
    featured_listing_days = Column(Integer, default=0)  # Featured in listings
    extra_product_slots = Column(Integer, default=0)  # Additional product slots
    extra_file_storage_mb = Column(Integer, default=0)  # Additional file storage
    
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    purchases = relationship("Purchase", back_populates="in_app_purchase")
    
    def __repr__(self):
        return f"<InAppPurchase(id={self.id}, name='{self.name}', price={self.price})>"


class Purchase(Base):
    __tablename__ = "purchases"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    vendor_id = Column(UUID(as_uuid=True), ForeignKey("vendors.id"), nullable=False, index=True)
    purchase_type = Column(Enum(PurchaseType), nullable=False)
    
    # Foreign keys (one will be null based on purchase_type)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey("subscriptions.id"), nullable=True, index=True)
    in_app_purchase_id = Column(UUID(as_uuid=True), ForeignKey("in_app_purchases.id"), nullable=True, index=True)
    
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default="TZS")
    
    # Purchase metadata
    metadata = Column(JSON, nullable=True)  # Additional purchase information
    
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    vendor = relationship("Vendor", backref="purchases")
    subscription = relationship("Subscription", back_populates="purchases")
    in_app_purchase = relationship("InAppPurchase", back_populates="purchases")
    payments = relationship("Payment", back_populates="purchase", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Purchase(id={self.id}, vendor_id={self.vendor_id}, type={self.purchase_type})>"


class Payment(Base):
    __tablename__ = "payments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    purchase_id = Column(UUID(as_uuid=True), ForeignKey("purchases.id"), nullable=False, index=True)
    
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default="TZS")
    payment_method = Column(Enum(PaymentMethod), nullable=False)
    status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING, nullable=False)
    
    # External payment reference
    external_reference = Column(String(255), nullable=True, index=True)  # MPesa transaction ID, etc.
    gateway_response = Column(JSON, nullable=True)  # Full gateway response
    
    # Payment details
    phone_number = Column(String(20), nullable=True)  # For mobile payments
    account_number = Column(String(100), nullable=True)  # For bank transfers
    
    # Timestamps
    paid_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Admin notes
    admin_notes = Column(Text, nullable=True)
    processed_by = Column(UUID(as_uuid=True), ForeignKey("vendors.id"), nullable=True)  # Admin who processed
    
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    purchase = relationship("Purchase", back_populates="payments")
    processor = relationship("Vendor", foreign_keys=[processed_by], backref="processed_payments")
    
    def __repr__(self):
        return f"<Payment(id={self.id}, purchase_id={self.purchase_id}, status={self.status})>"
